"""
Physics-Driven Consensus Engine (PDCE) for CELEST AI

This module implements the physics-based labeling system for identifying
geo-effective CME events in solar wind data. It uses established solar physics
principles to generate ground-truth labels for training the ML model.

Key physics parameters:
- Bz (southward magnetic field component): Strong southward Bz indicates geo-effective ICME
- B_total (magnetic field magnitude): Enhanced field strength during ICME passage
- Proton speed: High-speed solar wind associated with CME shocks
- Proton density: Density enhancements in ICME structures
- Proton temperature: Temperature depressions in magnetic clouds
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional
import logging
from dataclasses import dataclass

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@dataclass
class PDCEConfig:
    """Configuration parameters for the Physics-Driven Consensus Engine"""
    
    # Magnetic field thresholds
    bz_threshold: float = -5.0  # nT, southward Bz threshold
    b_total_threshold: float = 10.0  # nT, enhanced field magnitude
    
    # Solar wind speed thresholds
    speed_threshold: float = 450.0  # km/s, high-speed threshold
    speed_shock_threshold: float = 600.0  # km/s, shock-like speeds
    
    # Density thresholds
    density_threshold: float = 10.0  # p/cc, enhanced density
    density_enhancement_factor: float = 2.0  # Factor above background
    
    # Temperature thresholds
    temp_depression_factor: float = 0.5  # Factor below expected temperature
    
    # Temporal parameters
    event_window_hours: int = 3  # Hours to label around detected conditions
    min_duration_minutes: int = 30  # Minimum duration for valid event
    
    # Probabilistic parameters (for PDCE v2)
    confidence_threshold: float = 0.7
    use_probabilistic: bool = False


class PhysicsDrivenConsensusEngine:
    """
    Physics-Driven Consensus Engine for labeling geo-effective CME events
    
    This class implements both heuristic (v1) and probabilistic (v2) approaches
    for identifying ICME signatures in solar wind data.
    """
    
    def __init__(self, config: Optional[PDCEConfig] = None):
        self.config = config or PDCEConfig()
        logger.info(f"Initialized PDCE with config: {self.config}")
    
    def generate_labels_heuristic(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Enhanced Physics-Driven Consensus Engine (Proto-PDCE v2)

        Implements multi-instrument consensus logic with predictive labeling
        for 30-60 minute advance warning capability.

        Args:
            df: DataFrame with solar wind parameters
                Required columns: Bz_gsm, B_total, speed, density, temperature

        Returns:
            DataFrame with added 'event_label' and 'event_confidence' columns
        """
        logger.info("Generating labels using Enhanced Physics-Driven Consensus Engine...")

        # Validate required columns
        required_cols = ['Bz_gsm', 'B_total', 'speed', 'density']
        missing_cols = [col for col in required_cols if col not in df.columns]
        if missing_cols:
            raise ValueError(f"Missing required columns: {missing_cols}")

        # Create a copy to avoid modifying original data
        result_df = df.copy()

        # Initialize labels
        result_df['event_label'] = 0
        result_df['event_confidence'] = 0.0
        result_df['event_drivers'] = ''
        result_df['consensus_score'] = 0.0

        # Apply enhanced physics-based consensus
        consensus_results = self._enhanced_consensus_engine(result_df)

        # Apply consensus-based labeling with predictive window
        result_df = self._apply_predictive_labeling(result_df, consensus_results)

        # Add event drivers information
        result_df = self._add_enhanced_event_drivers(result_df, consensus_results)

        # Apply temporal smoothing and validation
        result_df = self._temporal_validation(result_df)

        n_events = result_df['event_label'].sum()
        event_rate = result_df['event_label'].mean()
        avg_confidence = result_df[result_df['event_label'] == 1]['event_confidence'].mean()

        logger.info(f"Enhanced PDCE generated {n_events} positive labels ({event_rate:.3f} rate)")
        logger.info(f"Average event confidence: {avg_confidence:.3f}")

        return result_df
    
    def _evaluate_physics_conditions(self, df: pd.DataFrame) -> Dict[str, pd.Series]:
        """Evaluate individual physics-based conditions"""
        
        conditions = {}
        
        # Magnetic field conditions
        conditions['strong_southward_bz'] = df['Bz_gsm'] < self.config.bz_threshold
        conditions['moderate_southward_bz'] = df['Bz_gsm'] < (self.config.bz_threshold / 2)
        conditions['enhanced_field'] = df['B_total'] > self.config.b_total_threshold
        
        # Solar wind speed conditions
        conditions['high_speed'] = df['speed'] > self.config.speed_threshold
        conditions['shock_speed'] = df['speed'] > self.config.speed_shock_threshold
        
        # Density conditions
        conditions['enhanced_density'] = df['density'] > self.config.density_threshold
        
        # Calculate background density for enhancement detection
        if len(df) > 100:  # Need sufficient data for background calculation
            background_density = df['density'].rolling(window=100, center=True).median()
            conditions['density_enhancement'] = (
                df['density'] > background_density * self.config.density_enhancement_factor
            )
        else:
            conditions['density_enhancement'] = conditions['enhanced_density']
        
        # Temperature depression (if temperature data available)
        if 'temperature' in df.columns:
            # Expected temperature from speed (empirical relation)
            expected_temp = (df['speed'] / 10) ** 2  # Simplified relation
            conditions['temp_depression'] = (
                df['temperature'] < expected_temp * self.config.temp_depression_factor
            )
        else:
            conditions['temp_depression'] = pd.Series(False, index=df.index)
        
        return conditions

    def _enhanced_consensus_engine(self, df: pd.DataFrame) -> Dict[str, pd.Series]:
        """
        Enhanced multi-instrument consensus engine implementing PDCE logic

        Combines multiple physical indicators with Bayesian-inspired weighting
        to generate high-confidence CME predictions.
        """
        logger.info("Running Enhanced Consensus Engine...")

        # Calculate rolling statistics for trend detection
        window_short = 10   # 10 minutes
        window_medium = 30  # 30 minutes
        window_long = 60    # 1 hour

        consensus = {}

        # 1. Magnetic Field Consensus (MAG instrument proxy)
        consensus['bz_shock_signature'] = (
            (df['Bz_gsm'].rolling(window_short).min() < -8.0) &  # Strong southward excursion
            (df['Bz_gsm'].rolling(window_short).std() > 3.0)     # High variability (shock)
        )

        consensus['magnetic_enhancement'] = (
            (df['B_total'] > df['B_total'].rolling(window_long).quantile(0.8)) &  # Enhanced field
            (df['B_total'].rolling(window_medium).mean() > 12.0)                   # Sustained enhancement
        )

        # 2. Plasma Consensus (SWIS instrument proxy)
        consensus['density_spike'] = (
            (df['density'] > df['density'].rolling(window_long).quantile(0.9)) &  # Density enhancement
            (df['density'].rolling(window_short).max() > 15.0)                     # Absolute threshold
        )

        consensus['speed_shock'] = (
            (df['speed'].diff().rolling(window_short).max() > 100) |               # Rapid acceleration
            (df['speed'].rolling(window_medium).mean() > 500)                      # High sustained speed
        )

        # 3. Temperature Depression (Magnetic Cloud signature)
        if 'temperature' in df.columns:
            expected_temp = (df['speed'] / 10) ** 2  # Empirical relation
            consensus['temp_depression'] = (
                df['temperature'] < expected_temp * 0.6  # Significant depression
            )
        else:
            consensus['temp_depression'] = pd.Series(False, index=df.index)

        # 4. Dynamic Pressure Enhancement
        if 'dynamic_pressure' in df.columns:
            consensus['pressure_enhancement'] = (
                df['dynamic_pressure'] > df['dynamic_pressure'].rolling(window_long).quantile(0.85)
            )
        else:
            # Calculate dynamic pressure if not available
            dynamic_pressure = 1.67e-6 * df['density'] * df['speed']**2
            consensus['pressure_enhancement'] = (
                dynamic_pressure > dynamic_pressure.rolling(window_long).quantile(0.85)
            )

        # 5. Multi-instrument Consensus Score (Bayesian-inspired weighting)
        weights = {
            'bz_shock_signature': 0.25,      # Highest weight - most reliable indicator
            'magnetic_enhancement': 0.20,    # Strong indicator
            'speed_shock': 0.20,             # Strong indicator
            'density_spike': 0.15,           # Moderate indicator
            'pressure_enhancement': 0.10,    # Supporting indicator
            'temp_depression': 0.10          # Supporting indicator
        }

        consensus_score = pd.Series(0.0, index=df.index)
        for condition, weight in weights.items():
            consensus_score += consensus[condition].astype(float) * weight

        consensus['consensus_score'] = consensus_score

        # 6. High-confidence events (multiple instruments agree)
        consensus['high_confidence'] = consensus_score >= 0.6
        consensus['medium_confidence'] = (consensus_score >= 0.4) & (consensus_score < 0.6)
        consensus['low_confidence'] = (consensus_score >= 0.2) & (consensus_score < 0.4)

        return consensus

    def _apply_predictive_labeling(self, df: pd.DataFrame, consensus: Dict[str, pd.Series]) -> pd.DataFrame:
        """
        Apply predictive labeling with 30-60 minute advance warning window

        This implements the core PDCE concept: label data points BEFORE the event
        occurs to enable advance warning capability.
        """
        # Predictive window: label 30-60 minutes before event peak
        warning_window_min = 30  # minutes
        warning_window_max = 60  # minutes

        # Find high-confidence consensus events
        event_peaks = consensus['high_confidence']
        medium_events = consensus['medium_confidence']

        # Create predictive labels by shifting events backward in time
        predictive_labels = pd.Series(0, index=df.index)
        predictive_confidence = pd.Series(0.0, index=df.index)

        # High-confidence events: label 30-60 minutes before
        for idx in df[event_peaks].index:
            start_warn = max(0, idx - warning_window_max)
            end_warn = max(0, idx - warning_window_min)

            if start_warn < end_warn:
                predictive_labels.iloc[start_warn:end_warn] = 1
                predictive_confidence.iloc[start_warn:end_warn] = 0.9

        # Medium-confidence events: label 30 minutes before with lower confidence
        for idx in df[medium_events].index:
            start_warn = max(0, idx - warning_window_min)
            end_warn = max(0, idx - 10)  # Shorter window for medium confidence

            if start_warn < end_warn:
                # Only label if not already labeled with high confidence
                mask = (predictive_labels.iloc[start_warn:end_warn] == 0)
                predictive_labels.iloc[start_warn:end_warn][mask] = 1
                predictive_confidence.iloc[start_warn:end_warn][mask] = 0.6

        df['event_label'] = predictive_labels
        df['event_confidence'] = predictive_confidence
        df['consensus_score'] = consensus['consensus_score']

        return df

    def _add_enhanced_event_drivers(self, df: pd.DataFrame, consensus: Dict[str, pd.Series]) -> pd.DataFrame:
        """Add detailed event driver information based on consensus results"""

        drivers_list = []

        for idx in df.index:
            drivers = []

            if consensus['bz_shock_signature'].iloc[idx]:
                bz_val = df.loc[idx, 'Bz_gsm']
                drivers.append(f"Bz shock signature ({bz_val:.1f} nT)")

            if consensus['magnetic_enhancement'].iloc[idx]:
                b_val = df.loc[idx, 'B_total']
                drivers.append(f"Magnetic enhancement ({b_val:.1f} nT)")

            if consensus['speed_shock'].iloc[idx]:
                speed_val = df.loc[idx, 'speed']
                drivers.append(f"Speed shock ({speed_val:.0f} km/s)")

            if consensus['density_spike'].iloc[idx]:
                density_val = df.loc[idx, 'density']
                drivers.append(f"Density spike ({density_val:.1f} p/cc)")

            if consensus['pressure_enhancement'].iloc[idx]:
                drivers.append("Dynamic pressure enhancement")

            if consensus['temp_depression'].iloc[idx]:
                drivers.append("Temperature depression")

            # Add consensus score
            score = consensus['consensus_score'].iloc[idx]
            if score > 0:
                drivers.append(f"Consensus: {score:.2f}")

            drivers_list.append("; ".join(drivers))

        df['event_drivers'] = drivers_list
        return df

    def _temporal_validation(self, df: pd.DataFrame) -> pd.DataFrame:
        """Apply temporal validation and smoothing to reduce false positives"""

        # Minimum event duration (reduce very short spikes)
        min_duration = 15  # minutes

        # Find continuous event segments
        event_changes = df['event_label'].diff().fillna(0)
        event_starts = df[event_changes == 1].index
        event_ends = df[event_changes == -1].index

        # Handle edge cases
        if len(event_starts) > 0 and (len(event_ends) == 0 or event_starts[0] < event_ends[0]):
            event_ends = list(event_ends) + [df.index[-1]]
        if len(event_ends) > 0 and (len(event_starts) == 0 or event_ends[0] < event_starts[0]):
            event_starts = [df.index[0]] + list(event_starts)

        # Filter by minimum duration
        validated_labels = df['event_label'].copy()

        for start, end in zip(event_starts, event_ends):
            duration = end - start + 1
            if duration < min_duration:
                validated_labels.iloc[start:end+1] = 0

        df['event_label'] = validated_labels

        return df
    
    def _add_event_drivers(self, df: pd.DataFrame, conditions: Dict[str, pd.Series]) -> pd.DataFrame:
        """Add information about what physics conditions drove each event"""
        
        drivers_list = []
        
        for idx in df.index:
            drivers = []
            
            if conditions['strong_southward_bz'].iloc[idx]:
                drivers.append(f"Strong southward Bz ({df.loc[idx, 'Bz_gsm']:.1f} nT)")
            elif conditions['moderate_southward_bz'].iloc[idx]:
                drivers.append(f"Moderate southward Bz ({df.loc[idx, 'Bz_gsm']:.1f} nT)")
            
            if conditions['enhanced_field'].iloc[idx]:
                drivers.append(f"Enhanced B-field ({df.loc[idx, 'B_total']:.1f} nT)")
            
            if conditions['shock_speed'].iloc[idx]:
                drivers.append(f"Shock-like speed ({df.loc[idx, 'speed']:.0f} km/s)")
            elif conditions['high_speed'].iloc[idx]:
                drivers.append(f"High speed ({df.loc[idx, 'speed']:.0f} km/s)")
            
            if conditions['enhanced_density'].iloc[idx]:
                drivers.append(f"Enhanced density ({df.loc[idx, 'density']:.1f} p/cc)")
            
            if conditions['temp_depression'].iloc[idx]:
                drivers.append("Temperature depression")
            
            drivers_list.append("; ".join(drivers))
        
        df['event_drivers'] = drivers_list
        return df
    
    def _expand_event_windows(self, df: pd.DataFrame) -> pd.DataFrame:
        """Expand event labels to windows around detected conditions"""
        
        if df['event_label'].sum() == 0:
            return df
        
        # Convert window hours to data points (assuming 1-minute resolution)
        window_points = self.config.event_window_hours * 60
        
        # Find event indices
        event_indices = df[df['event_label'] == 1].index
        
        # Expand windows
        expanded_labels = df['event_label'].copy()
        expanded_confidence = df['event_confidence'].copy()
        
        for event_idx in event_indices:
            start_idx = max(0, event_idx - window_points // 2)
            end_idx = min(len(df), event_idx + window_points // 2)
            
            # Only expand if not already labeled with higher confidence
            mask = (expanded_confidence.iloc[start_idx:end_idx] < df.loc[event_idx, 'event_confidence'])
            expanded_labels.iloc[start_idx:end_idx][mask] = 1
            expanded_confidence.iloc[start_idx:end_idx][mask] = df.loc[event_idx, 'event_confidence'] * 0.8
        
        df['event_label'] = expanded_labels
        df['event_confidence'] = expanded_confidence
        
        return df
    
    def _filter_by_duration(self, df: pd.DataFrame) -> pd.DataFrame:
        """Filter events by minimum duration requirement"""
        
        if df['event_label'].sum() == 0:
            return df
        
        # Find continuous event segments
        event_changes = df['event_label'].diff().fillna(0)
        event_starts = df[event_changes == 1].index
        event_ends = df[event_changes == -1].index
        
        # Handle edge cases
        if len(event_starts) > len(event_ends):
            event_ends = event_ends.append(pd.Index([df.index[-1]]))
        elif len(event_ends) > len(event_starts):
            event_starts = pd.Index([df.index[0]]).append(event_starts)
        
        # Filter by duration
        min_duration_points = self.config.min_duration_minutes
        filtered_labels = df['event_label'].copy()
        
        for start, end in zip(event_starts, event_ends):
            duration = end - start + 1
            if duration < min_duration_points:
                filtered_labels.iloc[start:end+1] = 0
        
        df['event_label'] = filtered_labels
        
        return df
    
    def generate_labels_probabilistic(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Generate probabilistic labels using Bayesian inference (PDCE v2)
        
        This is a placeholder for the advanced probabilistic implementation
        that would use PyMC for Bayesian modeling.
        """
        logger.info("Probabilistic labeling not yet implemented. Using heuristic approach.")
        return self.generate_labels_heuristic(df)
    
    def validate_labels(self, df: pd.DataFrame, ground_truth_events: Optional[List[Tuple]] = None) -> Dict:
        """
        Validate generated labels against known events or physics principles
        
        Args:
            df: DataFrame with generated labels
            ground_truth_events: List of (start_time, end_time) tuples for known events
        
        Returns:
            Dictionary with validation metrics
        """
        metrics = {
            'total_events': df['event_label'].sum(),
            'event_rate': df['event_label'].mean(),
            'avg_confidence': df[df['event_label'] == 1]['event_confidence'].mean(),
            'physics_consistency': self._check_physics_consistency(df)
        }
        
        if ground_truth_events:
            # TODO: Implement comparison with ground truth catalog
            pass
        
        return metrics
    
    def _check_physics_consistency(self, df: pd.DataFrame) -> float:
        """Check if labeled events are physically consistent"""
        
        if df['event_label'].sum() == 0:
            return 1.0
        
        event_data = df[df['event_label'] == 1]
        
        # Check basic physics consistency
        consistent_events = 0
        total_events = len(event_data)
        
        for idx, row in event_data.iterrows():
            is_consistent = True
            
            # Check if Bz is southward during events
            if row['Bz_gsm'] > 0:
                is_consistent = False
            
            # Check if field magnitude is reasonable
            if row['B_total'] < abs(row['Bz_gsm']):
                is_consistent = False
            
            # Check if speed is reasonable
            if row['speed'] < 200 or row['speed'] > 1000:
                is_consistent = False
            
            if is_consistent:
                consistent_events += 1
        
        return consistent_events / total_events if total_events > 0 else 1.0


def main():
    """Example usage of the PDCE"""
    
    # Create sample data for testing
    np.random.seed(42)
    n_points = 1000
    
    sample_data = pd.DataFrame({
        'timestamp': pd.date_range('2020-01-01', periods=n_points, freq='1min'),
        'Bz_gsm': np.random.normal(-2, 5, n_points),
        'B_total': np.random.normal(8, 3, n_points),
        'speed': np.random.normal(400, 50, n_points),
        'density': np.random.normal(5, 2, n_points),
        'temperature': np.random.normal(100000, 20000, n_points)
    })
    
    # Add some artificial events
    event_indices = [200, 500, 800]
    for idx in event_indices:
        sample_data.loc[idx:idx+60, 'Bz_gsm'] = np.random.normal(-8, 2, 61)
        sample_data.loc[idx:idx+60, 'B_total'] = np.random.normal(15, 3, 61)
        sample_data.loc[idx:idx+60, 'speed'] = np.random.normal(500, 30, 61)
    
    # Initialize PDCE and generate labels
    pdce = PhysicsDrivenConsensusEngine()
    labeled_data = pdce.generate_labels_heuristic(sample_data)
    
    # Validate results
    metrics = pdce.validate_labels(labeled_data)
    
    print("PDCE Results:")
    print(f"Total events detected: {metrics['total_events']}")
    print(f"Event rate: {metrics['event_rate']:.3f}")
    print(f"Average confidence: {metrics['avg_confidence']:.3f}")
    print(f"Physics consistency: {metrics['physics_consistency']:.3f}")


if __name__ == "__main__":
    main()
