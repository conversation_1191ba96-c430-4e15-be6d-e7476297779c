# CELEST AI - FastAPI Service Dockerfile
# Multi-stage build for optimized production image

# Build stage
FROM python:3.10-slim as builder

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1

# Install system dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Create virtual environment
RUN python -m venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"

# Copy requirements and install Python dependencies
COPY environment.yml /tmp/
RUN pip install --upgrade pip && \
    pip install conda-env-to-requirements && \
    conda-env-to-requirements /tmp/environment.yml > /tmp/requirements.txt && \
    pip install -r /tmp/requirements.txt

# Production stage
FROM python:3.10-slim as production

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PATH="/opt/venv/bin:$PATH" \
    PYTHONPATH="/app/src:$PYTHONPATH"

# Install runtime dependencies
RUN apt-get update && apt-get install -y \
    curl \
    && rm -rf /var/lib/apt/lists/* \
    && groupadd -r celest && useradd -r -g celest celest

# Copy virtual environment from builder stage
COPY --from=builder /opt/venv /opt/venv

# Set working directory
WORKDIR /app

# Copy application code
COPY src/ /app/src/
COPY models/ /app/models/ 2>/dev/null || true
COPY config/ /app/config/ 2>/dev/null || true

# Create necessary directories
RUN mkdir -p /app/logs /app/data /app/mlruns && \
    chown -R celest:celest /app

# Copy startup script
COPY <<EOF /app/start.sh
#!/bin/bash
set -e

# Wait for dependencies (if any)
echo "Starting CELEST AI API..."

# Run database migrations or model loading if needed
echo "Loading models..."

# Start the FastAPI application
exec uvicorn src.api:app \
    --host 0.0.0.0 \
    --port 8000 \
    --workers 1 \
    --log-level info \
    --access-log \
    --loop uvloop
EOF

RUN chmod +x /app/start.sh && chown celest:celest /app/start.sh

# Switch to non-root user
USER celest

# Expose port
EXPOSE 8000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8000/ || exit 1

# Default command
CMD ["/app/start.sh"]

# Labels for metadata
LABEL maintainer="CELEST AI Team" \
      version="0.1.0" \
      description="CELEST AI FastAPI service for CME detection" \
      org.opencontainers.image.source="https://github.com/team/celest-ai"
