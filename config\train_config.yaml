# CELEST AI Training Configuration
# Configuration for PatchTST model training on synthetic CME data

# Data configuration
data_path: "data/processed/training_data_2010_2011.parquet"
test_size: 0.2
val_size: 0.1

# Model architecture
sequence_length: 180  # 3 hours of 1-minute data
patch_size: 12       # 12-minute patches
d_model: 128         # Model dimension
n_heads: 8           # Number of attention heads
n_layers: 6          # Number of transformer layers
dropout: 0.1         # Dropout rate

# Training parameters
learning_rate: 1e-4
max_epochs: 20       # Reduced for quick testing on synthetic data
batch_size: 32

# Early stopping
patience: 5

# Experiment tracking
experiment_name: "celest-ai-synthetic-data"
run_name: "patchtst-baseline"

# Model checkpointing
save_top_k: 3
monitor_metric: "val_loss"
