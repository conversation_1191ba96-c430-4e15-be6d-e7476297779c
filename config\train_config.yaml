# CELEST AI Training Configuration - REVISED FOR STABLE TRAINING
# Configuration for PatchTST model training with improved convergence

# Data configuration
data_path: "data/processed/training_data_2010_2011.parquet"
test_size: 0.2
val_size: 0.1

# Model architecture
sequence_length: 180  # 3 hours of 1-minute data
patch_size: 12       # 12-minute patches
d_model: 128         # Model dimension
n_heads: 8           # Number of attention heads
n_layers: 6          # Number of transformer layers
dropout: 0.2         # Increased from 0.1 to prevent overfitting

# Training parameters - REVISED FOR STABILITY
learning_rate: 1e-5  # Drastically reduced from 1e-4 for stability
max_epochs: 30       # Increased to allow for slower but stable learning
batch_size: 64       # Increased batch size for more stable gradients

# Early stopping
patience: 5

# Learning rate scheduler parameters
lr_scheduler_patience: 3    # Wait 3 epochs before reducing LR
lr_scheduler_factor: 0.5    # Reduce LR by half when plateau detected
lr_scheduler_verbose: true  # Print messages when LR is reduced

# Target performance
target_f1: 0.82

# Experiment tracking
experiment_name: "celest-ai-stable-training"
run_name: "patchtst-improved-convergence"

# Model checkpointing
save_top_k: 3
monitor_metric: "val_loss"
