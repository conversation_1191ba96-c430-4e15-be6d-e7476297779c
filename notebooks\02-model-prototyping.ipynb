{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# CELEST AI - Model Prototyping Notebook\n", "\n", "This notebook focuses on developing and testing machine learning models for CME detection, with emphasis on the PatchTST transformer architecture.\n", "\n", "## Objectives:\n", "1. Prepare data for machine learning training\n", "2. Implement baseline models for comparison\n", "3. Develop and test PatchTST architecture\n", "4. Evaluate model performance and interpretability\n", "5. Optimize hyperparameters\n", "6. Generate model explanations with SHAP"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import required libraries\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import torch\n", "import torch.nn as nn\n", "import torch.optim as optim\n", "from torch.utils.data import DataLoader\n", "import pytorch_lightning as pl\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.preprocessing import StandardScaler\n", "from sklearn.ensemble import RandomForestClassifier\n", "from sklearn.linear_model import LogisticRegression\n", "from sklearn.metrics import classification_report, confusion_matrix, roc_auc_score, roc_curve\n", "import lightgbm as lgb\n", "import shap\n", "from datetime import datetime\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Set up plotting\n", "plt.style.use('seaborn-v0_8')\n", "%matplotlib inline\n", "\n", "# Add src to path for imports\n", "import sys\n", "sys.path.append('../src')\n", "\n", "from model_train import CMEDataset, PatchTSTModel\n", "from preprocessing import add_ml_features\n", "from pdce import PhysicsDrivenConsensusEngine, PDCEConfig\n", "\n", "print(\"✅ Libraries imported successfully\")\n", "print(f\"PyTorch version: {torch.__version__}\")\n", "print(f\"CUDA available: {torch.cuda.is_available()}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Data Preparation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load and prepare data\n", "try:\n", "    # Load processed training data\n", "    training_data = pd.read_parquet('../data/processed/training_data_2010_2012.parquet')\n", "    print(\"✅ Loaded existing training data\")\nexcept FileNotFoundError:\n", "    print(\"⚠️ Training data not found. Creating sample data...\")\n", "    \n", "    # Create sample data for demonstration\n", "    np.random.seed(42)\n", "    n_points = 10000\n", "    dates = pd.date_range('2010-01-01', periods=n_points, freq='1min')\n", "    \n", "    training_data = pd.DataFrame({\n", "        'timestamp': dates,\n", "        'Bz_gsm': np.random.normal(-1, 3, n_points),\n", "        'B_total': np.random.normal(8, 2, n_points),\n", "        'speed': np.random.normal(400, 40, n_points),\n", "        'density': np.random.normal(5, 1.5, n_points),\n", "        'temperature': np.random.normal(100000, 15000, n_points)\n", "    })\n", "    \n", "    # Add derived features\n", "    training_data['dynamic_pressure'] = 1.67e-6 * training_data['density'] * training_data['speed']**2\n", "    training_data['clock_angle'] = np.random.uniform(-180, 180, n_points)\n", "    \n", "    # Apply PDCE labeling\n", "    pdce = PhysicsDrivenConsensusEngine()\n", "    training_data = pdce.generate_labels_heuristic(training_data)\n", "    \n", "    print(\"✅ Sample training data created\")\n", "\n", "print(f\"Training data shape: {training_data.shape}\")\n", "print(f\"Positive labels: {training_data['event_label'].sum()} ({training_data['event_label'].mean():.3f})\")\n", "\n", "# Display basic info\n", "training_data.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Add ML features for enhanced model performance\n", "print(\"Adding ML features...\")\n", "enhanced_data = add_ml_features(training_data.copy())\n", "\n", "print(f\"Original features: {len(training_data.columns)}\")\n", "print(f\"Enhanced features: {len(enhanced_data.columns)}\")\n", "print(f\"Added {len(enhanced_data.columns) - len(training_data.columns)} new features\")\n", "\n", "# Select core features for modeling\n", "core_features = [\n", "    'Bz_gsm', 'B_total', 'speed', 'density', 'temperature',\n", "    'dynamic_pressure', 'clock_angle'\n", "]\n", "\n", "# Add some rolling features\n", "rolling_features = [col for col in enhanced_data.columns if '_mean_' in col or '_std_' in col]\n", "selected_features = core_features + rolling_features[:10]  # Limit to avoid overfitting\n", "\n", "# Filter available features\n", "available_features = [col for col in selected_features if col in enhanced_data.columns]\n", "print(f\"Selected features for modeling: {len(available_features)}\")\n", "print(f\"Features: {available_features}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Baseline Models"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Prepare data for baseline models\n", "# Remove rows with missing values in selected features\n", "model_data = enhanced_data[available_features + ['event_label']].dropna()\n", "\n", "X = model_data[available_features]\n", "y = model_data['event_label']\n", "\n", "print(f\"Model data shape: {X.shape}\")\n", "print(f\"Class distribution: {y.value_counts().to_dict()}\")\n", "\n", "# Split data temporally (important for time series)\n", "split_idx = int(len(X) * 0.8)\n", "X_train, X_test = X.iloc[:split_idx], X.iloc[split_idx:]\n", "y_train, y_test = y.iloc[:split_idx], y.iloc[split_idx:]\n", "\n", "# Further split training data for validation\n", "X_train, X_val, y_train, y_val = train_test_split(\n", "    X_train, y_train, test_size=0.2, random_state=42, stratify=y_train\n", ")\n", "\n", "print(f\"Train: {X_train.shape}, Val: {X_val.shape}, Test: {X_test.shape}\")\n", "\n", "# Scale features\n", "scaler = StandardScaler()\n", "X_train_scaled = scaler.fit_transform(X_train)\n", "X_val_scaled = scaler.transform(X_val)\n", "X_test_scaled = scaler.transform(X_test)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Train baseline models\n", "baseline_models = {}\n", "baseline_results = {}\n", "\n", "print(\"=== Training Baseline Models ===\")\n", "\n", "# 1. Logistic Regression\n", "print(\"Training Logistic Regression...\")\n", "lr_model = LogisticRegression(random_state=42, class_weight='balanced')\n", "lr_model.fit(X_train_scaled, y_train)\n", "lr_pred = lr_model.predict(X_val_scaled)\n", "lr_prob = lr_model.predict_proba(X_val_scaled)[:, 1]\n", "\n", "baseline_models['Logistic Regression'] = lr_model\n", "baseline_results['Logistic Regression'] = {\n", "    'predictions': lr_pred,\n", "    'probabilities': lr_prob,\n", "    'auc': roc_auc_score(y_val, lr_prob)\n", "}\n", "\n", "# 2. <PERSON>\n", "print(\"Training Random Forest...\")\n", "rf_model = RandomForestClassifier(\n", "    n_estimators=100, random_state=42, class_weight='balanced', n_jobs=-1\n", ")\n", "rf_model.fit(X_train, y_train)\n", "rf_pred = rf_model.predict(X_val)\n", "rf_prob = rf_model.predict_proba(X_val)[:, 1]\n", "\n", "baseline_models['Random Forest'] = rf_model\n", "baseline_results['Random Forest'] = {\n", "    'predictions': rf_pred,\n", "    'probabilities': rf_prob,\n", "    'auc': roc_auc_score(y_val, rf_prob)\n", "}\n", "\n", "# 3. LightGBM\n", "print(\"Training LightGBM...\")\n", "lgb_train = lgb.Dataset(X_train, label=y_train)\n", "lgb_val = lgb.Dataset(X_val, label=y_val, reference=lgb_train)\n", "\n", "lgb_params = {\n", "    'objective': 'binary',\n", "    'metric': 'auc',\n", "    'boosting_type': 'gbdt',\n", "    'num_leaves': 31,\n", "    'learning_rate': 0.05,\n", "    'feature_fraction': 0.9,\n", "    'bagging_fraction': 0.8,\n", "    'bagging_freq': 5,\n", "    'verbose': -1,\n", "    'random_state': 42,\n", "    'class_weight': 'balanced'\n", "}\n", "\n", "lgb_model = lgb.train(\n", "    lgb_params,\n", "    lgb_train,\n", "    valid_sets=[lgb_val],\n", "    num_boost_round=100,\n", "    callbacks=[lgb.early_stopping(10), lgb.log_evaluation(0)]\n", ")\n", "\n", "lgb_prob = lgb_model.predict(X_val, num_iteration=lgb_model.best_iteration)\n", "lgb_pred = (lgb_prob > 0.5).astype(int)\n", "\n", "baseline_models['LightGBM'] = lgb_model\n", "baseline_results['LightGBM'] = {\n", "    'predictions': lgb_pred,\n", "    'probabilities': lgb_prob,\n", "    'auc': roc_auc_score(y_val, lgb_prob)\n", "}\n", "\n", "print(\"✅ Baseline models trained\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Evaluate baseline models\n", "print(\"=== Baseline Model Results ===\")\n", "\n", "fig, axes = plt.subplots(1, 3, figsize=(15, 5))\n", "\n", "# ROC curves\n", "for i, (name, results) in enumerate(baseline_results.items()):\n", "    fpr, tpr, _ = roc_curve(y_val, results['probabilities'])\n", "    auc = results['auc']\n", "    \n", "    axes[0].plot(fpr, tpr, label=f'{name} (AUC = {auc:.3f})')\n", "    \n", "    # Classification report\n", "    print(f\"\\n{name}:\")\n", "    print(classification_report(y_val, results['predictions']))\n", "\n", "axes[0].plot([0, 1], [0, 1], 'k--', alpha=0.5)\n", "axes[0].set_xlabel('False Positive Rate')\n", "axes[0].set_ylabel('True Positive Rate')\n", "axes[0].set_title('ROC Curves')\n", "axes[0].legend()\n", "axes[0].grid(True, alpha=0.3)\n", "\n", "# Feature importance (Random Forest)\n", "rf_importance = pd.DataFrame({\n", "    'feature': available_features,\n", "    'importance': baseline_models['Random Forest'].feature_importances_\n", "}).sort_values('importance', ascending=True)\n", "\n", "axes[1].barh(rf_importance['feature'][-10:], rf_importance['importance'][-10:])\n", "axes[1].set_title('Top 10 Features (Random Forest)')\n", "axes[1].set_xlabel('Importance')\n", "\n", "# Model comparison\n", "model_names = list(baseline_results.keys())\n", "model_aucs = [baseline_results[name]['auc'] for name in model_names]\n", "\n", "axes[2].bar(model_names, model_aucs, alpha=0.7)\n", "axes[2].set_title('Model Comparison (AUC)')\n", "axes[2].set_ylabel('AUC Score')\n", "axes[2].set_ylim(0.5, 1.0)\n", "plt.xticks(rotation=45)\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# Best baseline model\n", "best_baseline = max(baseline_results.items(), key=lambda x: x[1]['auc'])\n", "print(f\"\\n🏆 Best baseline model: {best_baseline[0]} (AUC = {best_baseline[1]['auc']:.3f})\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "name": "python", "nbformat": 4, "nbformat_minor": 4}}, "nbformat": 4, "nbformat_minor": 4}