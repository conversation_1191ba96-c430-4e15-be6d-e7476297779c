{"cells": [{"cell_type": "markdown", "metadata": {"id": "header"}, "source": ["# 🌞 CELEST AI: PatchTST Training on Google Colab\n", "\n", "[![Open In Colab](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/your-repo/celest-ai/blob/main/notebooks/celest_ai_training_colab.ipynb)\n", "\n", "This notebook trains the PatchTST transformer model for CME detection using Google Colab's free GPU.\n", "\n", "## Setup Instructions:\n", "1. **Enable GPU**: Runtime → Change runtime type → GPU\n", "2. **Upload Data**: Upload your `training_data_2010_2011.parquet` file\n", "3. **Run All Cells**: Runtime → Run all\n", "\n", "## Expected Results:\n", "- 🎯 F1 Score > 0.82 target\n", "- 📊 MLflow experiment tracking\n", "- 💾 Model artifacts for deployment\n", "- 🚀 Ready for real data integration"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "setup"}, "outputs": [], "source": ["# Install required packages\n", "!pip install pytorch-lightning mlflow shap plotly -q\n", "\n", "# Mount Google Drive (optional - for data storage)\n", "from google.colab import drive\n", "drive.mount('/content/drive')\n", "\n", "import pandas as pd\n", "import numpy as np\n", "import torch\n", "import torch.nn as nn\n", "import torch.optim as optim\n", "from torch.utils.data import Dataset, DataLoader\n", "import pytorch_lightning as pl\n", "from pytorch_lightning.callbacks import ModelCheckpoint, EarlyStopping\n", "from sklearn.preprocessing import StandardScaler\n", "from sklearn.metrics import classification_report, confusion_matrix, roc_auc_score, f1_score\n", "import mlflow\n", "import mlflow.pytorch\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from typing import Dict, List, Tuple, Optional\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "print(f\"🔥 PyTorch version: {torch.__version__}\")\n", "print(f\"⚡ CUDA available: {torch.cuda.is_available()}\")\n", "if torch.cuda.is_available():\n", "    print(f\"🚀 GPU device: {torch.cuda.get_device_name(0)}\")\n", "    print(f\"💾 GPU memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB\")\n", "else:\n", "    print(\"⚠️  No GPU detected - Enable GPU in Runtime settings!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "upload_data"}, "outputs": [], "source": ["# Upload training data\n", "from google.colab import files\n", "\n", "print(\"📁 Upload your training_data_2010_2011.parquet file:\")\n", "uploaded = files.upload()\n", "\n", "# Get the uploaded file path\n", "data_path = list(uploaded.keys())[0]\n", "print(f\"✅ Data uploaded: {data_path}\")\n", "\n", "# Configuration\n", "CONFIG = {\n", "    'data_path': data_path,\n", "    'sequence_length': 180,  # 3 hours of 1-minute data\n", "    'patch_size': 12,       # 12-minute patches\n", "    'd_model': 128,         # Model dimension\n", "    'n_heads': 8,           # Number of attention heads\n", "    'n_layers': 6,          # Number of transformer layers\n", "    'dropout': 0.1,         # Dropout rate\n", "    'learning_rate': 1e-4,\n", "    'max_epochs': 30,       # Increased for better training\n", "    'batch_size': 64,       # Larger batch size for GPU\n", "    'test_size': 0.2,\n", "    'val_size': 0.1,\n", "    'target_f1': 0.82       # Target F1 score\n", "}\n", "\n", "print(\"\\n⚙️  Training Configuration:\")\n", "for key, value in CONFIG.items():\n", "    print(f\"   {key}: {value}\")"]}], "metadata": {"accelerator": "GPU", "colab": {"gpuType": "T4", "provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}