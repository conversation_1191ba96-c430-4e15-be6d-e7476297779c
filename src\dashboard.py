"""
Streamlit Dashboard for CELEST AI

This module provides an interactive web dashboard for the CELEST AI system.
It allows operators to monitor real-time solar wind conditions, view alerts,
and understand model predictions through explainable AI visualizations.

Key features:
- Real-time data visualization
- Alert status display
- Model prediction interface
- SHAP explanations
- Historical event analysis
"""

import streamlit as st
import pandas as pd
import numpy as np
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import requests
import json
from datetime import datetime, timedelta
import time
import logging
from typing import Dict, List, Optional
import io

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Page configuration
st.set_page_config(
    page_title="CELEST AI - CME Detection Dashboard",
    page_icon="🌞",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS for styling
st.markdown("""
<style>
    .alert-box {
        padding: 1rem;
        border-radius: 0.5rem;
        margin: 1rem 0;
        font-weight: bold;
        text-align: center;
    }
    .alert-normal {
        background-color: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
    }
    .alert-watch {
        background-color: #fff3cd;
        color: #856404;
        border: 1px solid #ffeaa7;
    }
    .alert-alert {
        background-color: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
    }
    .metric-card {
        background-color: #f8f9fa;
        padding: 1rem;
        border-radius: 0.5rem;
        border: 1px solid #dee2e6;
    }
</style>
""", unsafe_allow_html=True)


class CelestDashboard:
    """Main dashboard class for CELEST AI"""
    
    def __init__(self):
        self.api_base_url = "http://localhost:8000"
        self.initialize_session_state()
    
    def initialize_session_state(self):
        """Initialize Streamlit session state variables"""
        if 'current_data' not in st.session_state:
            st.session_state.current_data = None
        if 'last_prediction' not in st.session_state:
            st.session_state.last_prediction = None
        if 'prediction_history' not in st.session_state:
            st.session_state.prediction_history = []
        if 'auto_refresh' not in st.session_state:
            st.session_state.auto_refresh = False
    
    def run(self):
        """Main dashboard application"""
        
        # Sidebar
        self.render_sidebar()
        
        # Main content
        st.title("🌞 CELEST AI - CME Detection Dashboard")
        st.markdown("**Real-time Geo-effective CME Detection System**")
        
        # Check API connection
        api_status = self.check_api_connection()
        
        if not api_status:
            st.error("⚠️ Cannot connect to CELEST AI API. Please ensure the API server is running.")
            st.stop()
        
        # Main dashboard layout
        self.render_main_dashboard()
    
    def render_sidebar(self):
        """Render the sidebar with controls and settings"""
        
        st.sidebar.header("🛠️ Controls")
        
        # Data input section
        st.sidebar.subheader("Data Input")
        
        input_method = st.sidebar.radio(
            "Select input method:",
            ["Upload File", "Generate Sample Data", "Real-time Simulation"]
        )
        
        if input_method == "Upload File":
            uploaded_file = st.sidebar.file_uploader(
                "Upload solar wind data (CSV/Parquet)",
                type=['csv', 'parquet'],
                help="Upload a file with columns: timestamp, Bz_gsm, B_total, speed, density"
            )
            
            if uploaded_file is not None:
                try:
                    if uploaded_file.name.endswith('.csv'):
                        data = pd.read_csv(uploaded_file)
                    else:
                        data = pd.read_parquet(uploaded_file)
                    
                    data['timestamp'] = pd.to_datetime(data['timestamp'])
                    st.session_state.current_data = data
                    st.sidebar.success(f"✅ Loaded {len(data)} data points")
                    
                except Exception as e:
                    st.sidebar.error(f"Error loading file: {e}")
        
        elif input_method == "Generate Sample Data":
            if st.sidebar.button("Generate Sample Data"):
                st.session_state.current_data = self.generate_sample_data()
                st.sidebar.success("✅ Sample data generated")
        
        elif input_method == "Real-time Simulation":
            st.sidebar.info("Real-time simulation mode")
            if st.sidebar.button("Start Simulation"):
                st.session_state.current_data = self.generate_sample_data()
                st.session_state.auto_refresh = True
        
        # Prediction controls
        st.sidebar.subheader("Prediction")
        
        if st.sidebar.button("🔮 Make Prediction", disabled=st.session_state.current_data is None):
            self.make_prediction()
        
        # Settings
        st.sidebar.subheader("Settings")
        
        show_explanations = st.sidebar.checkbox("Show AI Explanations", value=True)
        show_raw_data = st.sidebar.checkbox("Show Raw Data", value=False)
        
        # Auto-refresh
        if st.session_state.auto_refresh:
            if st.sidebar.button("Stop Auto-refresh"):
                st.session_state.auto_refresh = False
            else:
                time.sleep(5)
                st.experimental_rerun()
    
    def render_main_dashboard(self):
        """Render the main dashboard content"""
        
        # Alert status section
        self.render_alert_status()
        
        # Data visualization section
        if st.session_state.current_data is not None:
            self.render_data_visualization()
        else:
            st.info("👆 Please load data using the sidebar controls to begin analysis.")
        
        # Prediction history
        if st.session_state.prediction_history:
            self.render_prediction_history()
    
    def render_alert_status(self):
        """Render the current alert status"""
        
        st.header("🚨 Current Alert Status")
        
        if st.session_state.last_prediction is None:
            st.markdown("""
            <div class="alert-box alert-normal">
                <h3>SYSTEM READY</h3>
                <p>No recent predictions. Load data and make a prediction to see alert status.</p>
            </div>
            """, unsafe_allow_html=True)
            return
        
        pred = st.session_state.last_prediction
        status = pred.get('status', 'UNKNOWN')
        confidence = pred.get('confidence', 0.0)
        impact_time = pred.get('predicted_impact_time_minutes', 0)
        drivers = pred.get('drivers', [])
        
        # Alert box styling based on status
        if status == "ALERT":
            alert_class = "alert-alert"
            icon = "🔴"
        elif status == "WATCH":
            alert_class = "alert-watch"
            icon = "🟡"
        else:
            alert_class = "alert-normal"
            icon = "🟢"
        
        st.markdown(f"""
        <div class="alert-box {alert_class}">
            <h2>{icon} {status}</h2>
            <p>Confidence: {confidence:.1%}</p>
            {f"<p>Predicted Impact: {impact_time} minutes</p>" if impact_time > 0 else ""}
        </div>
        """, unsafe_allow_html=True)
        
        # Metrics row
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric("Alert Status", status)
        
        with col2:
            st.metric("Confidence", f"{confidence:.1%}")
        
        with col3:
            st.metric("Impact Time", f"{impact_time} min" if impact_time > 0 else "N/A")
        
        with col4:
            st.metric("Drivers", len(drivers))
        
        # Physical drivers
        if drivers:
            st.subheader("🔍 Physical Drivers")
            for i, driver in enumerate(drivers, 1):
                st.write(f"{i}. {driver}")
    
    def render_data_visualization(self):
        """Render data visualization plots"""
        
        st.header("📊 Solar Wind Data Analysis")
        
        data = st.session_state.current_data
        
        # Time range selector
        col1, col2 = st.columns(2)
        with col1:
            hours_back = st.selectbox("Time range to display:", [1, 3, 6, 12, 24], index=2)
        
        # Filter data to recent time window
        if 'timestamp' in data.columns:
            end_time = data['timestamp'].max()
            start_time = end_time - timedelta(hours=hours_back)
            plot_data = data[data['timestamp'] >= start_time].copy()
        else:
            plot_data = data.tail(hours_back * 60)  # Assume 1-minute resolution
        
        # Create subplots
        fig = make_subplots(
            rows=4, cols=1,
            subplot_titles=('Magnetic Field Components', 'Solar Wind Speed', 
                          'Proton Density', 'Dynamic Pressure'),
            vertical_spacing=0.08,
            shared_xaxes=True
        )
        
        # Magnetic field plot
        if 'Bz_gsm' in plot_data.columns:
            fig.add_trace(
                go.Scatter(x=plot_data['timestamp'], y=plot_data['Bz_gsm'],
                          name='Bz (GSM)', line=dict(color='red')),
                row=1, col=1
            )
        
        if 'B_total' in plot_data.columns:
            fig.add_trace(
                go.Scatter(x=plot_data['timestamp'], y=plot_data['B_total'],
                          name='|B| Total', line=dict(color='blue')),
                row=1, col=1
            )
        
        # Solar wind speed
        if 'speed' in plot_data.columns:
            fig.add_trace(
                go.Scatter(x=plot_data['timestamp'], y=plot_data['speed'],
                          name='Speed', line=dict(color='green')),
                row=2, col=1
            )
        
        # Proton density
        if 'density' in plot_data.columns:
            fig.add_trace(
                go.Scatter(x=plot_data['timestamp'], y=plot_data['density'],
                          name='Density', line=dict(color='orange')),
                row=3, col=1
            )
        
        # Dynamic pressure
        if 'dynamic_pressure' in plot_data.columns:
            fig.add_trace(
                go.Scatter(x=plot_data['timestamp'], y=plot_data['dynamic_pressure'],
                          name='Dynamic Pressure', line=dict(color='purple')),
                row=4, col=1
            )
        
        # Add threshold lines for CME detection
        fig.add_hline(y=-5, line_dash="dash", line_color="red", 
                     annotation_text="Bz Threshold", row=1, col=1)
        fig.add_hline(y=450, line_dash="dash", line_color="green",
                     annotation_text="Speed Threshold", row=2, col=1)
        
        # Update layout
        fig.update_layout(
            height=800,
            title_text="Solar Wind Parameters",
            showlegend=False
        )
        
        fig.update_yaxes(title_text="nT", row=1, col=1)
        fig.update_yaxes(title_text="km/s", row=2, col=1)
        fig.update_yaxes(title_text="p/cc", row=3, col=1)
        fig.update_yaxes(title_text="nPa", row=4, col=1)
        
        st.plotly_chart(fig, use_container_width=True)
        
        # Data statistics
        with st.expander("📈 Data Statistics"):
            col1, col2 = st.columns(2)
            
            with col1:
                st.subheader("Current Values")
                latest = plot_data.iloc[-1] if len(plot_data) > 0 else None
                if latest is not None:
                    for col in ['Bz_gsm', 'B_total', 'speed', 'density']:
                        if col in latest:
                            st.metric(col, f"{latest[col]:.2f}")
            
            with col2:
                st.subheader("Statistics")
                numeric_cols = plot_data.select_dtypes(include=[np.number]).columns
                st.dataframe(plot_data[numeric_cols].describe())
    
    def render_prediction_history(self):
        """Render prediction history"""
        
        st.header("📋 Prediction History")
        
        history_df = pd.DataFrame(st.session_state.prediction_history)
        
        if len(history_df) > 0:
            # Display recent predictions
            st.dataframe(
                history_df[['timestamp', 'status', 'confidence', 'predicted_impact_time_minutes']].tail(10),
                use_container_width=True
            )
            
            # Alert frequency chart
            status_counts = history_df['status'].value_counts()
            fig = px.pie(values=status_counts.values, names=status_counts.index,
                        title="Alert Status Distribution")
            st.plotly_chart(fig, use_container_width=True)
    
    def check_api_connection(self) -> bool:
        """Check if the API is accessible"""
        try:
            response = requests.get(f"{self.api_base_url}/", timeout=5)
            return response.status_code == 200
        except:
            return False
    
    def make_prediction(self):
        """Make a prediction using the API"""
        
        if st.session_state.current_data is None:
            st.error("No data available for prediction")
            return
        
        try:
            # Prepare data for API
            data = st.session_state.current_data
            
            # Take the most recent 180 minutes (3 hours) for prediction
            recent_data = data.tail(180)
            
            # Convert to API format
            data_points = []
            for _, row in recent_data.iterrows():
                point = {
                    "timestamp": row['timestamp'].isoformat() if 'timestamp' in row else datetime.now().isoformat(),
                    "Bz_gsm": float(row.get('Bz_gsm', 0)),
                    "B_total": float(row.get('B_total', 0)),
                    "speed": float(row.get('speed', 400)),
                    "density": float(row.get('density', 5)),
                    "temperature": float(row.get('temperature', 100000))
                }
                data_points.append(point)
            
            # Make API request
            payload = {"data": data_points}
            
            with st.spinner("Making prediction..."):
                response = requests.post(
                    f"{self.api_base_url}/api/v1/predict",
                    json=payload,
                    timeout=30
                )
            
            if response.status_code == 200:
                prediction = response.json()
                st.session_state.last_prediction = prediction
                st.session_state.prediction_history.append(prediction)
                st.success("✅ Prediction completed successfully!")
                st.experimental_rerun()
            else:
                st.error(f"API Error: {response.status_code} - {response.text}")
        
        except Exception as e:
            st.error(f"Error making prediction: {e}")
    
    def generate_sample_data(self) -> pd.DataFrame:
        """Generate sample solar wind data for demonstration"""
        
        np.random.seed(42)
        n_points = 300  # 5 hours of 1-minute data
        
        # Generate base time series
        timestamps = pd.date_range(
            start=datetime.now() - timedelta(hours=5),
            periods=n_points,
            freq='1min'
        )
        
        # Generate realistic solar wind parameters
        data = pd.DataFrame({
            'timestamp': timestamps,
            'Bz_gsm': np.random.normal(-1, 3, n_points),
            'B_total': np.random.normal(8, 2, n_points),
            'speed': np.random.normal(400, 40, n_points),
            'density': np.random.normal(5, 1.5, n_points),
            'temperature': np.random.normal(100000, 15000, n_points)
        })
        
        # Add a CME-like event in the recent past
        event_start = n_points - 60  # 1 hour ago
        event_end = n_points - 20    # 20 minutes ago
        
        data.loc[event_start:event_end, 'Bz_gsm'] = np.random.normal(-8, 1, event_end - event_start + 1)
        data.loc[event_start:event_end, 'speed'] = np.random.normal(550, 20, event_end - event_start + 1)
        data.loc[event_start:event_end, 'density'] = np.random.normal(12, 2, event_end - event_start + 1)
        
        # Calculate derived parameters
        data['dynamic_pressure'] = 1.67e-6 * data['density'] * data['speed']**2
        data['clock_angle'] = np.random.uniform(-180, 180, n_points)
        
        return data


def main():
    """Main function to run the dashboard"""
    dashboard = CelestDashboard()
    dashboard.run()


if __name__ == "__main__":
    main()
