#!/usr/bin/env python3
"""
Setup script for CELEST AI prototype

This script helps users quickly set up and run the CELEST AI system
for the first time, including environment setup, data acquisition,
and initial model training.
"""

import subprocess
import sys
import os
from pathlib import Path
import argparse
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def run_command(command, check=True, shell=False):
    """Run a command and handle errors"""
    try:
        if shell:
            result = subprocess.run(command, shell=True, check=check, capture_output=True, text=True)
        else:
            result = subprocess.run(command, check=check, capture_output=True, text=True)
        
        if result.stdout:
            logger.info(result.stdout)
        if result.stderr:
            logger.warning(result.stderr)
        
        return result
    except subprocess.CalledProcessError as e:
        logger.error(f"Command failed: {e}")
        if e.stdout:
            logger.error(f"STDOUT: {e.stdout}")
        if e.stderr:
            logger.error(f"STDERR: {e.stderr}")
        raise


def check_prerequisites():
    """Check if required tools are installed"""
    logger.info("Checking prerequisites...")
    
    required_tools = {
        'python': ['python', '--version'],
        'conda': ['conda', '--version'],
        'git': ['git', '--version']
    }
    
    missing_tools = []
    
    for tool, command in required_tools.items():
        try:
            run_command(command, check=True)
            logger.info(f"✅ {tool} is available")
        except (subprocess.CalledProcessError, FileNotFoundError):
            logger.error(f"❌ {tool} is not available")
            missing_tools.append(tool)
    
    if missing_tools:
        logger.error(f"Missing required tools: {missing_tools}")
        logger.error("Please install the missing tools and try again.")
        return False
    
    return True


def setup_environment():
    """Set up the conda environment"""
    logger.info("Setting up conda environment...")
    
    # Check if environment already exists
    try:
        result = run_command(['conda', 'env', 'list'], check=True)
        if 'celestai' in result.stdout:
            logger.info("Environment 'celestai' already exists")
            response = input("Do you want to recreate it? (y/N): ")
            if response.lower() == 'y':
                logger.info("Removing existing environment...")
                run_command(['conda', 'env', 'remove', '-n', 'celestai', '-y'], check=True)
            else:
                logger.info("Using existing environment")
                return True
    except subprocess.CalledProcessError:
        pass
    
    # Create environment
    logger.info("Creating conda environment from environment.yml...")
    run_command(['conda', 'env', 'create', '-f', 'environment.yml'], check=True)
    
    logger.info("✅ Environment setup complete")
    return True


def acquire_data(quick_test=True):
    """Run data acquisition pipeline"""
    logger.info("Running data acquisition pipeline...")
    
    # Activate environment and run data acquisition
    if quick_test:
        command = "conda run -n celestai python scripts/fetch_data.py --quick-test"
    else:
        command = "conda run -n celestai python scripts/fetch_data.py --start-year 2010 --end-year 2015"
    
    logger.info(f"Running: {command}")
    run_command(command, shell=True, check=True)
    
    logger.info("✅ Data acquisition complete")
    return True


def run_tests():
    """Run the test suite"""
    logger.info("Running test suite...")
    
    command = "conda run -n celestai python -m pytest tests/ -v"
    logger.info(f"Running: {command}")
    
    try:
        run_command(command, shell=True, check=True)
        logger.info("✅ All tests passed")
        return True
    except subprocess.CalledProcessError:
        logger.warning("⚠️ Some tests failed, but continuing setup...")
        return False


def train_initial_model():
    """Train an initial model"""
    logger.info("Training initial model...")
    
    # Check if training data exists
    training_data_path = Path("data/processed/training_data_2010_2011.parquet")
    if not training_data_path.exists():
        logger.warning("Training data not found. Skipping model training.")
        return False
    
    command = "conda run -n celestai python src/model_train.py --data data/processed/training_data_2010_2011.parquet"
    logger.info(f"Running: {command}")
    
    try:
        run_command(command, shell=True, check=True)
        logger.info("✅ Initial model training complete")
        return True
    except subprocess.CalledProcessError:
        logger.warning("⚠️ Model training failed, but setup can continue...")
        return False


def start_services():
    """Start the API and dashboard services"""
    logger.info("Starting services...")
    
    logger.info("To start the services manually, run:")
    logger.info("1. API Server: conda run -n celestai uvicorn src.api:app --reload")
    logger.info("2. Dashboard: conda run -n celestai streamlit run src/dashboard.py")
    logger.info("3. MLflow UI: conda run -n celestai mlflow ui")
    
    response = input("Do you want to start the API server now? (y/N): ")
    if response.lower() == 'y':
        logger.info("Starting API server...")
        logger.info("API will be available at http://localhost:8000")
        logger.info("API docs will be available at http://localhost:8000/docs")
        logger.info("Press Ctrl+C to stop the server")
        
        try:
            run_command("conda run -n celestai uvicorn src.api:app --reload", shell=True, check=False)
        except KeyboardInterrupt:
            logger.info("API server stopped")


def main():
    """Main setup function"""
    parser = argparse.ArgumentParser(description='CELEST AI Setup Script')
    parser.add_argument('--skip-env', action='store_true', help='Skip environment setup')
    parser.add_argument('--skip-data', action='store_true', help='Skip data acquisition')
    parser.add_argument('--skip-tests', action='store_true', help='Skip running tests')
    parser.add_argument('--skip-training', action='store_true', help='Skip model training')
    parser.add_argument('--full-data', action='store_true', help='Download full dataset (2010-2015)')
    parser.add_argument('--start-services', action='store_true', help='Start services after setup')
    
    args = parser.parse_args()
    
    logger.info("🌞 CELEST AI Setup Script")
    logger.info("=" * 50)
    
    try:
        # Check prerequisites
        if not check_prerequisites():
            sys.exit(1)
        
        # Setup environment
        if not args.skip_env:
            if not setup_environment():
                sys.exit(1)
        
        # Acquire data
        if not args.skip_data:
            if not acquire_data(quick_test=not args.full_data):
                sys.exit(1)
        
        # Run tests
        if not args.skip_tests:
            run_tests()
        
        # Train initial model
        if not args.skip_training:
            train_initial_model()
        
        # Start services
        if args.start_services:
            start_services()
        
        logger.info("🎉 CELEST AI setup completed successfully!")
        logger.info("")
        logger.info("Next steps:")
        logger.info("1. Activate environment: conda activate celestai")
        logger.info("2. Start API: uvicorn src.api:app --reload")
        logger.info("3. Start dashboard: streamlit run src/dashboard.py")
        logger.info("4. View API docs: http://localhost:8000/docs")
        logger.info("5. View dashboard: http://localhost:8501")
        logger.info("")
        logger.info("For Docker deployment:")
        logger.info("docker-compose up -d")
        
    except KeyboardInterrupt:
        logger.info("Setup interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Setup failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
