#!/usr/bin/env python3
"""
Data Preparation Script for CELEST AI Training

This script prepares the training data for upload to Kaggle or Google Colab.
It validates the data format and creates a compressed version for faster upload.
"""

import pandas as pd
import numpy as np
from pathlib import Path
import argparse
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def validate_training_data(data_path: str) -> bool:
    """
    Validate the training data format and content
    
    Args:
        data_path: Path to the training data file
    
    Returns:
        True if data is valid, False otherwise
    """
    try:
        logger.info(f"Validating data: {data_path}")
        
        # Load data
        data = pd.read_parquet(data_path)
        logger.info(f"Loaded {len(data)} records")
        
        # Check required columns
        required_columns = [
            'timestamp', 'event_label', 'Bz_gsm', 'B_total', 
            'speed', 'density', 'temperature'
        ]
        
        missing_columns = [col for col in required_columns if col not in data.columns]
        if missing_columns:
            logger.error(f"Missing required columns: {missing_columns}")
            return False
        
        # Check data types
        if not pd.api.types.is_datetime64_any_dtype(data['timestamp']):
            logger.error("timestamp column must be datetime type")
            return False
        
        # Check target distribution
        if 'event_label' in data.columns:
            target_dist = data['event_label'].value_counts()
            positive_rate = target_dist.get(1, 0) / len(data)
            logger.info(f"Target distribution: {dict(target_dist)}")
            logger.info(f"Positive rate: {positive_rate:.3f}")
            
            if positive_rate < 0.01 or positive_rate > 0.99:
                logger.warning(f"Unusual target distribution: {positive_rate:.3f}")
        
        # Check for missing values
        missing_stats = data.isnull().sum()
        if missing_stats.sum() > 0:
            logger.info("Missing values per column:")
            for col, count in missing_stats[missing_stats > 0].items():
                logger.info(f"  {col}: {count} ({count/len(data)*100:.1f}%)")
        
        # Check date range
        date_range = data['timestamp'].max() - data['timestamp'].min()
        logger.info(f"Date range: {data['timestamp'].min()} to {data['timestamp'].max()}")
        logger.info(f"Duration: {date_range.days} days")
        
        logger.info("✅ Data validation passed!")
        return True
        
    except Exception as e:
        logger.error(f"Data validation failed: {e}")
        return False


def prepare_for_upload(input_path: str, output_path: str = None) -> str:
    """
    Prepare data for upload to Kaggle/Colab
    
    Args:
        input_path: Path to input data file
        output_path: Path for output file (optional)
    
    Returns:
        Path to prepared data file
    """
    if output_path is None:
        output_path = input_path.replace('.parquet', '_upload.parquet')
    
    logger.info(f"Preparing data for upload: {input_path} -> {output_path}")
    
    # Load data
    data = pd.read_parquet(input_path)
    
    # Optimize data types for smaller file size
    logger.info("Optimizing data types...")
    
    # Convert float64 to float32 where possible
    float_cols = data.select_dtypes(include=['float64']).columns
    for col in float_cols:
        data[col] = data[col].astype('float32')
    
    # Convert int64 to int32 where possible
    int_cols = data.select_dtypes(include=['int64']).columns
    for col in int_cols:
        if col != 'timestamp':  # Keep timestamp as is
            if data[col].min() >= -2147483648 and data[col].max() <= 2147483647:
                data[col] = data[col].astype('int32')
    
    # Save optimized data
    data.to_parquet(output_path, compression='snappy')
    
    # Compare file sizes
    original_size = Path(input_path).stat().st_size / (1024 * 1024)  # MB
    new_size = Path(output_path).stat().st_size / (1024 * 1024)  # MB
    
    logger.info(f"File size: {original_size:.1f} MB -> {new_size:.1f} MB")
    logger.info(f"Size reduction: {(1 - new_size/original_size)*100:.1f}%")
    
    return output_path


def create_data_summary(data_path: str) -> dict:
    """
    Create a summary of the training data
    
    Args:
        data_path: Path to the data file
    
    Returns:
        Dictionary with data summary
    """
    data = pd.read_parquet(data_path)
    
    summary = {
        'total_records': len(data),
        'date_range': {
            'start': data['timestamp'].min().isoformat(),
            'end': data['timestamp'].max().isoformat(),
            'duration_days': (data['timestamp'].max() - data['timestamp'].min()).days
        },
        'features': list(data.columns),
        'target_distribution': dict(data['event_label'].value_counts()) if 'event_label' in data.columns else {},
        'missing_values': dict(data.isnull().sum()),
        'file_size_mb': Path(data_path).stat().st_size / (1024 * 1024)
    }
    
    return summary


def main():
    """Main function"""
    parser = argparse.ArgumentParser(description='Prepare CELEST AI training data for upload')
    parser.add_argument('--input', '-i', required=True, 
                       help='Path to input training data file')
    parser.add_argument('--output', '-o', 
                       help='Path for output file (optional)')
    parser.add_argument('--validate-only', action='store_true',
                       help='Only validate data, do not prepare for upload')
    
    args = parser.parse_args()
    
    # Validate input file exists
    if not Path(args.input).exists():
        logger.error(f"Input file not found: {args.input}")
        return 1
    
    # Validate data
    if not validate_training_data(args.input):
        logger.error("Data validation failed!")
        return 1
    
    if args.validate_only:
        logger.info("Validation complete!")
        return 0
    
    # Prepare for upload
    output_path = prepare_for_upload(args.input, args.output)
    
    # Create summary
    summary = create_data_summary(output_path)
    
    logger.info("\n📊 Data Summary:")
    logger.info(f"  Records: {summary['total_records']:,}")
    logger.info(f"  Duration: {summary['date_range']['duration_days']} days")
    logger.info(f"  Features: {len(summary['features'])}")
    logger.info(f"  File size: {summary['file_size_mb']:.1f} MB")
    
    if summary['target_distribution']:
        positive_rate = summary['target_distribution'].get(1, 0) / summary['total_records']
        logger.info(f"  Positive events: {positive_rate:.1%}")
    
    logger.info(f"\n✅ Data prepared for upload: {output_path}")
    logger.info("\n🚀 Next steps:")
    logger.info("  1. Upload to Kaggle dataset or Google Colab")
    logger.info("  2. Run the training notebook")
    logger.info("  3. Achieve F1 score > 0.82!")
    
    return 0


if __name__ == "__main__":
    exit(main())
