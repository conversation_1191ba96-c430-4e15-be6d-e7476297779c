"""
Test suite for Physics-Driven Consensus Engine (PDCE)

This module contains unit tests for the PDCE labeling system,
including physics-based heuristics and validation functions.
"""

import pytest
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import sys
from pathlib import Path

# Add src to path for imports
sys.path.append(str(Path(__file__).parent.parent / "src"))

from pdce import PhysicsDrivenConsensusEngine, PDCEConfig


class TestPDCEConfig:
    """Test cases for PDCE configuration"""
    
    def test_default_config(self):
        """Test default configuration values"""
        config = PDCEConfig()
        
        # Check default values are reasonable
        assert config.bz_threshold == -5.0
        assert config.b_total_threshold == 10.0
        assert config.speed_threshold == 450.0
        assert config.density_threshold == 10.0
        assert config.event_window_hours == 3
        assert config.min_duration_minutes == 30
        assert config.confidence_threshold == 0.7
        assert config.use_probabilistic == False
    
    def test_custom_config(self):
        """Test custom configuration"""
        config = PDCEConfig(
            bz_threshold=-8.0,
            speed_threshold=500.0,
            event_window_hours=2
        )
        
        assert config.bz_threshold == -8.0
        assert config.speed_threshold == 500.0
        assert config.event_window_hours == 2
        # Other values should remain default
        assert config.b_total_threshold == 10.0


class TestPhysicsDrivenConsensusEngine:
    """Test cases for the main PDCE class"""
    
    @pytest.fixture
    def pdce(self):
        """Create PDCE instance with default config"""
        return PhysicsDrivenConsensusEngine()
    
    @pytest.fixture
    def sample_solar_wind_data(self):
        """Create sample solar wind data for testing"""
        np.random.seed(42)
        n_points = 1000
        
        data = pd.DataFrame({
            'timestamp': pd.date_range('2020-01-01', periods=n_points, freq='1min'),
            'Bz_gsm': np.random.normal(-1, 3, n_points),
            'B_total': np.random.normal(8, 2, n_points),
            'speed': np.random.normal(400, 40, n_points),
            'density': np.random.normal(5, 1.5, n_points),
            'temperature': np.random.normal(100000, 15000, n_points)
        })
        
        return data
    
    @pytest.fixture
    def cme_event_data(self):
        """Create data with clear CME signatures"""
        np.random.seed(42)
        n_points = 200
        
        data = pd.DataFrame({
            'timestamp': pd.date_range('2020-01-01', periods=n_points, freq='1min'),
            'Bz_gsm': np.full(n_points, -8.0),  # Strong southward Bz
            'B_total': np.full(n_points, 15.0),  # Enhanced field magnitude
            'speed': np.full(n_points, 550.0),   # High speed
            'density': np.full(n_points, 12.0),  # Enhanced density
            'temperature': np.full(n_points, 80000.0)  # Reduced temperature
        })
        
        return data
    
    @pytest.fixture
    def quiet_period_data(self):
        """Create data representing quiet solar wind conditions"""
        np.random.seed(42)
        n_points = 200
        
        data = pd.DataFrame({
            'timestamp': pd.date_range('2020-01-01', periods=n_points, freq='1min'),
            'Bz_gsm': np.full(n_points, 2.0),    # Northward Bz
            'B_total': np.full(n_points, 5.0),   # Low field magnitude
            'speed': np.full(n_points, 350.0),   # Low speed
            'density': np.full(n_points, 3.0),   # Low density
            'temperature': np.full(n_points, 120000.0)  # Normal temperature
        })
        
        return data
    
    def test_pdce_initialization(self):
        """Test PDCE initialization"""
        pdce = PhysicsDrivenConsensusEngine()
        assert pdce.config is not None
        assert isinstance(pdce.config, PDCEConfig)
        
        # Test with custom config
        custom_config = PDCEConfig(bz_threshold=-10.0)
        pdce_custom = PhysicsDrivenConsensusEngine(custom_config)
        assert pdce_custom.config.bz_threshold == -10.0
    
    def test_missing_columns_error(self, pdce):
        """Test error handling for missing required columns"""
        incomplete_data = pd.DataFrame({
            'timestamp': pd.date_range('2020-01-01', periods=10, freq='1min'),
            'Bz_gsm': np.random.normal(0, 1, 10)
            # Missing B_total, speed, density
        })
        
        with pytest.raises(ValueError, match="Missing required columns"):
            pdce.generate_labels_heuristic(incomplete_data)
    
    def test_evaluate_physics_conditions(self, pdce, cme_event_data):
        """Test physics condition evaluation"""
        conditions = pdce._evaluate_physics_conditions(cme_event_data)
        
        # Check that all expected conditions are present
        expected_conditions = [
            'strong_southward_bz', 'moderate_southward_bz', 'enhanced_field',
            'high_speed', 'shock_speed', 'enhanced_density', 'density_enhancement'
        ]
        
        for condition in expected_conditions:
            assert condition in conditions
            assert isinstance(conditions[condition], pd.Series)
        
        # For CME event data, certain conditions should be True
        assert conditions['strong_southward_bz'].all()  # All points have Bz < -5
        assert conditions['enhanced_field'].all()       # All points have B > 10
        assert conditions['high_speed'].all()           # All points have speed > 450
    
    def test_cme_event_detection(self, pdce, cme_event_data):
        """Test detection of clear CME events"""
        labeled_data = pdce.generate_labels_heuristic(cme_event_data)
        
        # Should detect events in CME-like data
        assert 'event_label' in labeled_data.columns
        assert 'event_confidence' in labeled_data.columns
        assert 'event_drivers' in labeled_data.columns
        
        # Most points should be labeled as events
        event_rate = labeled_data['event_label'].mean()
        assert event_rate > 0.5, f"Expected high event rate, got {event_rate}"
        
        # Confidence should be high for clear events
        event_confidences = labeled_data[labeled_data['event_label'] == 1]['event_confidence']
        assert event_confidences.mean() > 0.7, "Expected high confidence for clear events"
    
    def test_quiet_period_detection(self, pdce, quiet_period_data):
        """Test that quiet periods are not labeled as events"""
        labeled_data = pdce.generate_labels_heuristic(quiet_period_data)
        
        # Should not detect events in quiet data
        event_rate = labeled_data['event_label'].mean()
        assert event_rate < 0.1, f"Expected low event rate for quiet period, got {event_rate}"
    
    def test_event_drivers_generation(self, pdce, cme_event_data):
        """Test generation of event driver descriptions"""
        labeled_data = pdce.generate_labels_heuristic(cme_event_data)
        
        # Check that event drivers are generated for detected events
        events = labeled_data[labeled_data['event_label'] == 1]
        
        if len(events) > 0:
            # Should have non-empty driver descriptions
            assert not events['event_drivers'].str.strip().eq('').all()
            
            # Check for expected driver keywords
            drivers_text = ' '.join(events['event_drivers'].values)
            assert 'Bz' in drivers_text or 'southward' in drivers_text
            assert 'speed' in drivers_text or 'field' in drivers_text
    
    def test_event_window_expansion(self, pdce):
        """Test event window expansion functionality"""
        # Create data with a single point meeting CME criteria
        data = pd.DataFrame({
            'timestamp': pd.date_range('2020-01-01', periods=300, freq='1min'),
            'Bz_gsm': np.full(300, 0.0),
            'B_total': np.full(300, 5.0),
            'speed': np.full(300, 350.0),
            'density': np.full(300, 3.0)
        })
        
        # Make one point clearly a CME signature
        data.loc[150, 'Bz_gsm'] = -10.0
        data.loc[150, 'B_total'] = 20.0
        data.loc[150, 'speed'] = 600.0
        data.loc[150, 'density'] = 15.0
        
        labeled_data = pdce.generate_labels_heuristic(data)
        
        # Should expand the event window around the detected point
        events = labeled_data[labeled_data['event_label'] == 1]
        assert len(events) > 1, "Event window should be expanded beyond single point"
        
        # Event should be centered around the original detection
        event_indices = events.index
        assert 150 in event_indices, "Original detection point should be included"
    
    def test_minimum_duration_filtering(self, pdce):
        """Test minimum duration filtering"""
        # Create data with very short event signatures
        data = pd.DataFrame({
            'timestamp': pd.date_range('2020-01-01', periods=100, freq='1min'),
            'Bz_gsm': np.full(100, 0.0),
            'B_total': np.full(100, 5.0),
            'speed': np.full(100, 350.0),
            'density': np.full(100, 3.0)
        })
        
        # Create a very short event (5 minutes)
        data.loc[50:54, 'Bz_gsm'] = -10.0
        data.loc[50:54, 'B_total'] = 20.0
        data.loc[50:54, 'speed'] = 600.0
        
        # Use strict minimum duration
        strict_config = PDCEConfig(min_duration_minutes=60)  # 1 hour minimum
        strict_pdce = PhysicsDrivenConsensusEngine(strict_config)
        
        labeled_data = strict_pdce.generate_labels_heuristic(data)
        
        # Short event should be filtered out
        assert labeled_data['event_label'].sum() == 0, "Short events should be filtered out"
    
    def test_physics_consistency_validation(self, pdce, sample_solar_wind_data):
        """Test physics consistency validation"""
        labeled_data = pdce.generate_labels_heuristic(sample_solar_wind_data)
        
        # Validate the labels
        validation_metrics = pdce.validate_labels(labeled_data)
        
        assert 'total_events' in validation_metrics
        assert 'event_rate' in validation_metrics
        assert 'physics_consistency' in validation_metrics
        
        # Physics consistency should be reasonable (> 0.5)
        assert validation_metrics['physics_consistency'] >= 0.5
    
    def test_check_physics_consistency(self, pdce):
        """Test physics consistency checking function"""
        # Create physically inconsistent data
        inconsistent_data = pd.DataFrame({
            'Bz_gsm': [5.0],      # Northward Bz
            'B_total': [3.0],     # But total field < |Bz| (impossible)
            'speed': [1500.0],    # Unrealistically high speed
            'density': [0.01],    # Very low density
            'event_label': [1]
        })
        
        consistency = pdce._check_physics_consistency(inconsistent_data)
        assert consistency < 0.5, "Should detect physics inconsistency"
        
        # Create physically consistent data
        consistent_data = pd.DataFrame({
            'Bz_gsm': [-5.0],
            'B_total': [8.0],     # Reasonable total field
            'speed': [450.0],     # Reasonable speed
            'density': [5.0],     # Reasonable density
            'event_label': [1]
        })
        
        consistency = pdce._check_physics_consistency(consistent_data)
        assert consistency >= 0.5, "Should recognize physics consistency"
    
    def test_empty_data_handling(self, pdce):
        """Test handling of empty datasets"""
        empty_data = pd.DataFrame(columns=['timestamp', 'Bz_gsm', 'B_total', 'speed', 'density'])
        
        # Should handle empty data gracefully
        labeled_data = pdce.generate_labels_heuristic(empty_data)
        assert len(labeled_data) == 0
        assert 'event_label' in labeled_data.columns
    
    def test_probabilistic_labeling_placeholder(self, pdce, sample_solar_wind_data):
        """Test probabilistic labeling (currently falls back to heuristic)"""
        # This tests the current placeholder implementation
        heuristic_result = pdce.generate_labels_heuristic(sample_solar_wind_data)
        probabilistic_result = pdce.generate_labels_probabilistic(sample_solar_wind_data)
        
        # Currently should be identical (placeholder implementation)
        pd.testing.assert_frame_equal(heuristic_result, probabilistic_result)


class TestPDCEIntegration:
    """Integration tests for PDCE with realistic scenarios"""
    
    def test_mixed_conditions_scenario(self):
        """Test PDCE with mixed quiet and active conditions"""
        np.random.seed(42)
        
        # Create 24 hours of data
        n_points = 24 * 60  # 1 minute resolution
        data = pd.DataFrame({
            'timestamp': pd.date_range('2020-01-01', periods=n_points, freq='1min'),
            'Bz_gsm': np.random.normal(-1, 2, n_points),
            'B_total': np.random.normal(6, 2, n_points),
            'speed': np.random.normal(380, 30, n_points),
            'density': np.random.normal(4, 1, n_points),
            'temperature': np.random.normal(110000, 20000, n_points)
        })
        
        # Insert a clear CME event (2 hours duration)
        event_start = 8 * 60  # 8 AM
        event_end = 10 * 60   # 10 AM
        
        data.loc[event_start:event_end, 'Bz_gsm'] = np.random.normal(-8, 1, event_end - event_start + 1)
        data.loc[event_start:event_end, 'speed'] = np.random.normal(520, 20, event_end - event_start + 1)
        data.loc[event_start:event_end, 'density'] = np.random.normal(12, 2, event_end - event_start + 1)
        data.loc[event_start:event_end, 'B_total'] = np.random.normal(14, 2, event_end - event_start + 1)
        
        # Apply PDCE
        pdce = PhysicsDrivenConsensusEngine()
        labeled_data = pdce.generate_labels_heuristic(data)
        
        # Should detect the inserted event
        assert labeled_data['event_label'].sum() > 0, "Should detect the inserted CME event"
        
        # Event should be detected around the inserted time period
        event_times = labeled_data[labeled_data['event_label'] == 1]['timestamp']
        event_hours = event_times.dt.hour
        
        # Should have events detected around 8-10 AM period
        assert (event_hours >= 7).any() and (event_hours <= 11).any(), \
            "Events should be detected around the inserted CME period"


if __name__ == "__main__":
    # Run tests if script is executed directly
    pytest.main([__file__, "-v"])
