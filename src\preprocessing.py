"""
Data Preprocessing Module for CELEST AI

This module handles data cleaning, feature engineering, and preparation
of solar wind data for training and inference. It processes OMNIWeb data
and integrates with ICME catalogs for ground truth labeling.

Key functions:
- Load and clean OMNIWeb data
- Handle missing values and fill values
- Feature engineering for time-series analysis
- Integration with Richardson & Cane ICME catalog
- Data splitting and preparation for ML training
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional, Union
import logging
from pathlib import Path
import requests
from datetime import datetime, timedelta
import warnings

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Suppress warnings for cleaner output
warnings.filterwarnings('ignore')


class OMNIWebDataProcessor:
    """
    Processor for OMNIWeb solar wind data
    
    Handles downloading, cleaning, and preprocessing of 1-minute resolution
    solar wind data from NASA's OMNIWeb service.
    """
    
    # OMNIWeb fill values that indicate missing data
    FILL_VALUES = {
        'Bx_gsm': 9999.99,
        'By_gsm': 9999.99, 
        'Bz_gsm': 9999.99,
        'B_total': 999.99,
        'speed': 9999.9,
        'density': 999.99,
        'temperature': 9999999.0,
        'pressure': 99.99
    }
    
    # Expected column names in OMNIWeb data
    OMNIWEB_COLUMNS = [
        'year', 'day', 'hour', 'minute',
        'Bx_gsm', 'By_gsm', 'Bz_gsm', 'B_total',
        'speed', 'density', 'temperature', 'pressure'
    ]
    
    def __init__(self, data_dir: str = "data/raw"):
        self.data_dir = Path(data_dir)
        self.data_dir.mkdir(parents=True, exist_ok=True)
        
    def load_omniweb_data(self, start_year: int, end_year: int, 
                         force_download: bool = False) -> pd.DataFrame:
        """
        Load OMNIWeb data for specified year range
        
        Args:
            start_year: Starting year for data
            end_year: Ending year for data  
            force_download: Whether to re-download existing files
            
        Returns:
            DataFrame with cleaned OMNIWeb data
        """
        logger.info(f"Loading OMNIWeb data from {start_year} to {end_year}")
        
        all_data = []
        
        for year in range(start_year, end_year + 1):
            year_data = self._load_year_data(year, force_download)
            if year_data is not None:
                all_data.append(year_data)
        
        if not all_data:
            raise ValueError("No data could be loaded for the specified years")
        
        # Combine all years
        combined_data = pd.concat(all_data, ignore_index=True)
        
        # Clean and process the data
        processed_data = self._clean_omniweb_data(combined_data)
        
        logger.info(f"Loaded {len(processed_data)} data points spanning {start_year}-{end_year}")
        
        return processed_data
    
    def _load_year_data(self, year: int, force_download: bool = False) -> Optional[pd.DataFrame]:
        """Load data for a single year"""
        
        file_path = self.data_dir / f"omniweb_{year}.txt"
        
        # Download if file doesn't exist or force_download is True
        if not file_path.exists() or force_download:
            success = self._download_year_data(year, file_path)
            if not success:
                logger.warning(f"Failed to download data for year {year}")
                return None
        
        try:
            # Read the data file
            data = pd.read_csv(file_path, delim_whitespace=True, 
                             names=self.OMNIWEB_COLUMNS, header=None)
            
            # Add year column if not present
            if 'year' not in data.columns:
                data['year'] = year
                
            return data
            
        except Exception as e:
            logger.error(f"Error reading data for year {year}: {e}")
            return None
    
    def _download_year_data(self, year: int, file_path: Path) -> bool:
        """
        Download OMNIWeb data for a specific year
        
        Note: This is a placeholder implementation. In practice, you would
        need to implement the actual OMNIWeb API calls or file downloads.
        """
        logger.info(f"Downloading OMNIWeb data for year {year}")
        
        # Placeholder: Create synthetic data for demonstration
        # In real implementation, this would download from OMNIWeb
        try:
            # Generate synthetic data that resembles real solar wind data
            np.random.seed(year)  # Reproducible data per year
            
            # Number of minutes in the year
            if year % 4 == 0:  # Leap year
                n_minutes = 366 * 24 * 60
            else:
                n_minutes = 365 * 24 * 60
            
            # Create time series
            start_date = datetime(year, 1, 1)
            dates = [start_date + timedelta(minutes=i) for i in range(n_minutes)]
            
            synthetic_data = []
            for i, date in enumerate(dates):
                # Add some realistic solar wind variations
                base_bz = np.random.normal(-1, 3)
                base_speed = np.random.normal(400, 50)
                base_density = np.random.normal(5, 2)
                base_temp = np.random.normal(100000, 20000)
                
                # Add occasional CME-like events
                if np.random.random() < 0.001:  # ~1 event per 1000 minutes
                    base_bz = np.random.normal(-8, 2)
                    base_speed = np.random.normal(550, 30)
                    base_density = np.random.normal(15, 3)
                
                b_total = np.sqrt(base_bz**2 + np.random.normal(0, 2)**2 + np.random.normal(0, 2)**2)
                
                row = [
                    date.year, date.timetuple().tm_yday, date.hour, date.minute,
                    np.random.normal(0, 2),  # Bx_gsm
                    np.random.normal(0, 2),  # By_gsm  
                    base_bz,  # Bz_gsm
                    max(0.1, b_total),  # B_total
                    max(200, base_speed),  # speed
                    max(0.1, base_density),  # density
                    max(10000, base_temp),  # temperature
                    np.random.normal(2, 0.5)  # pressure
                ]
                
                synthetic_data.append(row)
            
            # Save to file
            df = pd.DataFrame(synthetic_data, columns=self.OMNIWEB_COLUMNS)
            df.to_csv(file_path, sep=' ', index=False, header=False, 
                     float_format='%.2f')
            
            logger.info(f"Successfully created synthetic data for year {year}")
            return True
            
        except Exception as e:
            logger.error(f"Error creating data for year {year}: {e}")
            return False
    
    def _clean_omniweb_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """Clean and process raw OMNIWeb data"""
        
        logger.info("Cleaning OMNIWeb data...")
        
        # Create timestamp column
        data['timestamp'] = pd.to_datetime(
            data[['year', 'day', 'hour', 'minute']].apply(
                lambda x: datetime(int(x['year']), 1, 1) + 
                         timedelta(days=int(x['day'])-1, hours=int(x['hour']), 
                                 minutes=int(x['minute'])), axis=1
            )
        )
        
        # Sort by timestamp
        data = data.sort_values('timestamp').reset_index(drop=True)
        
        # Replace fill values with NaN
        for column, fill_value in self.FILL_VALUES.items():
            if column in data.columns:
                data[column] = data[column].replace(fill_value, np.nan)
        
        # Remove obviously invalid values
        data = self._remove_invalid_values(data)
        
        # Interpolate missing values
        data = self._interpolate_missing_values(data)
        
        # Add derived features
        data = self._add_derived_features(data)
        
        # Remove rows with too many missing values
        data = data.dropna(subset=['Bz_gsm', 'B_total', 'speed', 'density'])
        
        logger.info(f"Cleaned data: {len(data)} valid data points")
        
        return data
    
    def _remove_invalid_values(self, data: pd.DataFrame) -> pd.DataFrame:
        """Remove physically invalid values"""
        
        # Define reasonable ranges for solar wind parameters
        valid_ranges = {
            'Bz_gsm': (-50, 50),      # nT
            'B_total': (0, 100),       # nT  
            'speed': (200, 1000),      # km/s
            'density': (0.1, 100),     # p/cc
            'temperature': (1000, 2000000),  # K
            'pressure': (0.01, 50)     # nPa
        }
        
        for column, (min_val, max_val) in valid_ranges.items():
            if column in data.columns:
                invalid_mask = (data[column] < min_val) | (data[column] > max_val)
                data.loc[invalid_mask, column] = np.nan
        
        return data
    
    def _interpolate_missing_values(self, data: pd.DataFrame) -> pd.DataFrame:
        """Interpolate missing values using appropriate methods"""
        
        # Parameters for interpolation
        interpolation_columns = ['Bx_gsm', 'By_gsm', 'Bz_gsm', 'B_total', 
                               'speed', 'density', 'temperature', 'pressure']
        
        for column in interpolation_columns:
            if column in data.columns:
                # Use linear interpolation for short gaps (< 10 minutes)
                data[column] = data[column].interpolate(method='linear', limit=10)
                
                # For longer gaps, use forward/backward fill as last resort
                data[column] = data[column].ffill(limit=30)
                data[column] = data[column].bfill(limit=30)
        
        return data
    
    def _add_derived_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """Add derived features for enhanced analysis"""
        
        # Magnetic field features
        if all(col in data.columns for col in ['Bx_gsm', 'By_gsm', 'Bz_gsm']):
            # Clock angle (important for geomagnetic activity)
            data['clock_angle'] = np.arctan2(data['By_gsm'], data['Bz_gsm']) * 180 / np.pi
            
            # Magnetic field variance (indicator of turbulence)
            data['B_variance'] = data[['Bx_gsm', 'By_gsm', 'Bz_gsm']].rolling(
                window=60, center=True).std().mean(axis=1)
        
        # Solar wind features
        if all(col in data.columns for col in ['speed', 'density', 'temperature']):
            # Dynamic pressure
            data['dynamic_pressure'] = 1.67e-6 * data['density'] * data['speed']**2  # nPa
            
            # Plasma beta (ratio of thermal to magnetic pressure)
            if 'B_total' in data.columns:
                thermal_pressure = 1.38e-23 * data['density'] * 1e6 * data['temperature'] * 1e9  # nPa
                magnetic_pressure = data['B_total']**2 / (2 * 4e-7 * np.pi) * 1e9  # nPa
                data['plasma_beta'] = thermal_pressure / magnetic_pressure
        
        # Time-based features
        data['hour'] = data['timestamp'].dt.hour
        data['day_of_year'] = data['timestamp'].dt.dayofyear
        data['solar_cycle_phase'] = (data['timestamp'].dt.year - 1996) % 11  # Approximate solar cycle
        
        return data


class ICMECatalogProcessor:
    """
    Processor for ICME catalog data (Richardson & Cane catalog)
    """
    
    def __init__(self, data_dir: str = "data/raw"):
        self.data_dir = Path(data_dir)
        self.data_dir.mkdir(parents=True, exist_ok=True)
    
    def load_icme_catalog(self, force_download: bool = False) -> pd.DataFrame:
        """
        Load Richardson & Cane ICME catalog
        
        Args:
            force_download: Whether to re-download the catalog
            
        Returns:
            DataFrame with ICME events
        """
        catalog_file = self.data_dir / "richardson_cane_icme_catalog.txt"
        
        if not catalog_file.exists() or force_download:
            self._download_icme_catalog(catalog_file)
        
        return self._parse_icme_catalog(catalog_file)
    
    def _download_icme_catalog(self, file_path: Path):
        """Download ICME catalog (placeholder implementation)"""
        
        logger.info("Creating synthetic ICME catalog for demonstration")
        
        # Create synthetic ICME events for demonstration
        # In real implementation, this would download from the actual catalog
        np.random.seed(42)
        
        events = []
        start_date = datetime(2000, 1, 1)
        end_date = datetime(2020, 12, 31)
        
        # Generate ~20 events per year
        n_events = int((end_date - start_date).days / 365 * 20)
        
        for i in range(n_events):
            # Random event start time
            random_days = np.random.randint(0, (end_date - start_date).days)
            event_start = start_date + timedelta(days=random_days)
            
            # Event duration (6-48 hours)
            duration_hours = np.random.randint(6, 49)
            event_end = event_start + timedelta(hours=duration_hours)
            
            # Event properties
            quality = np.random.choice(['1', '2', '3'], p=[0.3, 0.5, 0.2])  # Quality rating
            
            events.append({
                'start_time': event_start,
                'end_time': event_end,
                'duration_hours': duration_hours,
                'quality': quality
            })
        
        # Save to file
        catalog_df = pd.DataFrame(events)
        catalog_df.to_csv(file_path, index=False)
        
        logger.info(f"Created synthetic ICME catalog with {len(events)} events")
    
    def _parse_icme_catalog(self, file_path: Path) -> pd.DataFrame:
        """Parse ICME catalog file"""
        
        try:
            catalog = pd.read_csv(file_path)
            catalog['start_time'] = pd.to_datetime(catalog['start_time'])
            catalog['end_time'] = pd.to_datetime(catalog['end_time'])
            
            logger.info(f"Loaded ICME catalog with {len(catalog)} events")
            return catalog
            
        except Exception as e:
            logger.error(f"Error parsing ICME catalog: {e}")
            return pd.DataFrame()


def create_training_dataset(omniweb_data: pd.DataFrame, 
                          icme_catalog: pd.DataFrame,
                          output_path: str = "data/processed/training_data.parquet") -> pd.DataFrame:
    """
    Create training dataset by combining OMNIWeb data with ICME catalog labels
    
    Args:
        omniweb_data: Processed OMNIWeb solar wind data
        icme_catalog: ICME event catalog
        output_path: Path to save the training dataset
        
    Returns:
        DataFrame ready for ML training
    """
    logger.info("Creating training dataset...")
    
    # Initialize labels
    training_data = omniweb_data.copy()
    training_data['icme_label'] = 0
    training_data['icme_event_id'] = -1
    
    # Label data points that fall within ICME events
    for idx, event in icme_catalog.iterrows():
        mask = (
            (training_data['timestamp'] >= event['start_time']) & 
            (training_data['timestamp'] <= event['end_time'])
        )
        training_data.loc[mask, 'icme_label'] = 1
        training_data.loc[mask, 'icme_event_id'] = idx
    
    # Add features for ML training
    training_data = add_ml_features(training_data)

    # Save to file if output_path is provided
    if output_path:
        output_dir = Path(output_path).parent
        output_dir.mkdir(parents=True, exist_ok=True)
        training_data.to_parquet(output_path)
    
    n_positive = training_data['icme_label'].sum()
    n_total = len(training_data)
    
    logger.info(f"Created training dataset: {n_total} samples, {n_positive} positive labels ({n_positive/n_total:.3f})")
    
    return training_data


def add_ml_features(data: pd.DataFrame) -> pd.DataFrame:
    """Add enhanced physics-informed features for ML training"""

    logger.info("Adding enhanced physics-informed features...")

    # Core physics features for CME detection
    data = add_physics_features(data)

    # Rolling statistics (important for time-series patterns)
    windows = [10, 30, 60, 180]  # minutes
    features = ['Bz_gsm', 'B_total', 'speed', 'density']

    for window in windows:
        for feature in features:
            if feature in data.columns:
                # Rolling mean and std
                data[f'{feature}_mean_{window}m'] = data[feature].rolling(window=window, center=True).mean()
                data[f'{feature}_std_{window}m'] = data[feature].rolling(window=window, center=True).std()

                # Rolling min and max
                data[f'{feature}_min_{window}m'] = data[feature].rolling(window=window, center=True).min()
                data[f'{feature}_max_{window}m'] = data[feature].rolling(window=window, center=True).max()

    # Enhanced gradient features (shock detection)
    for feature in features:
        if feature in data.columns:
            # Multiple time scales for gradient
            data[f'{feature}_gradient_1m'] = data[feature].diff(1)
            data[f'{feature}_gradient_5m'] = data[feature].diff(5)
            data[f'{feature}_gradient_10m'] = data[feature].diff(10)

            # Gradient magnitude
            data[f'{feature}_gradient_abs'] = data[feature].diff().abs()

            # Acceleration (second derivative)
            data[f'{feature}_acceleration'] = data[feature].diff().diff()

    # Lag features
    lags = [1, 5, 10, 30, 60]
    for lag in lags:
        for feature in features:
            if feature in data.columns:
                data[f'{feature}_lag_{lag}'] = data[feature].shift(lag)

    return data


def add_physics_features(data: pd.DataFrame) -> pd.DataFrame:
    """
    Add physics-informed features based on solar wind and CME physics

    These features make physical patterns explicit for the ML model
    """
    logger.info("Engineering physics-based features...")

    # 1. Magnetic field features (critical for CME detection)
    if all(col in data.columns for col in ['Bz_gsm', 'B_total']):
        # Bz persistence (how long Bz stays southward)
        data['bz_southward_persistence'] = (data['Bz_gsm'] < -3).rolling(30).sum() / 30

        # Bz intensity (most negative Bz in recent window)
        data['bz_intensity_10m'] = data['Bz_gsm'].rolling(10).min()
        data['bz_intensity_30m'] = data['Bz_gsm'].rolling(30).min()

        # Magnetic field ratios
        data['bz_to_btotal_ratio'] = np.abs(data['Bz_gsm']) / data['B_total']
        data['btotal_enhancement'] = data['B_total'] / data['B_total'].rolling(60).mean()

    # 2. Solar wind speed features (shock detection)
    if 'speed' in data.columns:
        # Speed acceleration (shock signature)
        data['speed_acceleration_5m'] = data['speed'].diff(5)
        data['speed_acceleration_10m'] = data['speed'].diff(10)

        # Speed variability (turbulence indicator)
        data['speed_variability_30m'] = data['speed'].rolling(30).std() / data['speed'].rolling(30).mean()

        # Speed enhancement over background
        data['speed_enhancement'] = data['speed'] / data['speed'].rolling(120).mean()

    # 3. Density features (plasma compression)
    if 'density' in data.columns:
        # Density spikes (compression regions)
        data['density_spike_indicator'] = (
            data['density'] > data['density'].rolling(60).quantile(0.9)
        ).astype(int)

        # Density enhancement
        data['density_enhancement'] = data['density'] / data['density'].rolling(60).mean()

        # Density gradient (compression fronts)
        data['density_gradient_5m'] = data['density'].diff(5)

    # 4. Combined physics features
    if all(col in data.columns for col in ['speed', 'density', 'B_total']):
        # Dynamic pressure
        data['dynamic_pressure'] = 1.67e-6 * data['density'] * data['speed']**2

        # Magnetic pressure
        data['magnetic_pressure'] = data['B_total']**2 / (2 * 4e-7 * np.pi)

        # Pressure ratios (plasma beta proxy)
        data['kinetic_to_magnetic_pressure'] = data['dynamic_pressure'] / data['magnetic_pressure']

        # Total pressure
        data['total_pressure'] = data['dynamic_pressure'] + data['magnetic_pressure']

    # 5. Temperature features (if available)
    if 'temperature' in data.columns and 'speed' in data.columns:
        # Expected temperature from speed (empirical relation)
        expected_temp = (data['speed'] / 10) ** 2
        data['temperature_depression'] = expected_temp / data['temperature']
        data['temperature_anomaly'] = (data['temperature'] - expected_temp) / expected_temp

    # 6. Multi-parameter interaction features
    if all(col in data.columns for col in ['Bz_gsm', 'speed', 'density']):
        # Bz-speed interaction (geoeffectiveness proxy)
        data['bz_speed_product'] = np.abs(data['Bz_gsm']) * data['speed']

        # Bz-density interaction
        data['bz_density_product'] = np.abs(data['Bz_gsm']) * data['density']

        # Combined geoeffectiveness indicator
        data['geoeffectiveness_proxy'] = (
            np.abs(data['Bz_gsm']) * data['speed'] * np.sqrt(data['density'])
        )

    # 7. Shock-related features
    if all(col in data.columns for col in ['speed', 'density', 'B_total']):
        # Mach number proxy (speed relative to local conditions)
        alfven_speed = data['B_total'] / np.sqrt(4e-7 * np.pi * data['density'] * 1.67e-27)
        data['mach_number_proxy'] = data['speed'] / (alfven_speed / 1000)  # Convert to km/s

        # Compression ratio
        data['compression_ratio'] = data['density'] / data['density'].shift(10)

    # Fill NaN values created by calculations
    data = data.fillna(method='ffill').fillna(method='bfill')

    logger.info(f"Added {len([col for col in data.columns if any(x in col for x in ['_enhancement', '_ratio', '_pressure', '_gradient'])])} physics features")

    return data


def main():
    """Example usage of the preprocessing module"""
    
    # Initialize processors
    omniweb_processor = OMNIWebDataProcessor()
    icme_processor = ICMECatalogProcessor()
    
    # Load data for a small test period
    omniweb_data = omniweb_processor.load_omniweb_data(2010, 2012)
    icme_catalog = icme_processor.load_icme_catalog()
    
    # Create training dataset
    training_data = create_training_dataset(omniweb_data, icme_catalog)
    
    print(f"Training dataset created with {len(training_data)} samples")
    print(f"Features: {list(training_data.columns)}")
    print(f"Positive label rate: {training_data['icme_label'].mean():.3f}")


if __name__ == "__main__":
    main()
