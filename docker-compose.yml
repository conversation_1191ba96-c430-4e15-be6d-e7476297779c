version: '3.8'

services:
  # CELEST AI FastAPI Service
  celest-api:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    container_name: celest-ai-api
    ports:
      - "8000:8000"
    environment:
      - PYTHONPATH=/app/src
      - MLFLOW_TRACKING_URI=http://mlflow:5000
      - LOG_LEVEL=info
    volumes:
      - ./data:/app/data:ro
      - ./models:/app/models:ro
      - ./logs:/app/logs
      - mlflow-data:/app/mlruns
    depends_on:
      - mlflow
    restart: unless-stopped
    networks:
      - celest-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # MLflow Tracking Server
  mlflow:
    image: python:3.10-slim
    container_name: celest-mlflow
    ports:
      - "5000:5000"
    environment:
      - MLFLOW_BACKEND_STORE_URI=sqlite:///mlflow/mlflow.db
      - MLFLOW_DEFAULT_ARTIFACT_ROOT=/mlflow/artifacts
    volumes:
      - mlflow-data:/mlflow
    command: >
      bash -c "
        pip install mlflow[extras] &&
        mlflow server 
        --backend-store-uri sqlite:///mlflow/mlflow.db 
        --default-artifact-root /mlflow/artifacts 
        --host 0.0.0.0 
        --port 5000
      "
    restart: unless-stopped
    networks:
      - celest-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Streamlit Dashboard
  celest-dashboard:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    container_name: celest-ai-dashboard
    ports:
      - "8501:8501"
    environment:
      - PYTHONPATH=/app/src
      - CELEST_API_URL=http://celest-api:8000
    volumes:
      - ./data:/app/data:ro
      - ./logs:/app/logs
    depends_on:
      - celest-api
    command: streamlit run src/dashboard.py --server.port=8501 --server.address=0.0.0.0
    restart: unless-stopped
    networks:
      - celest-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8501/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Nginx Reverse Proxy (Optional)
  nginx:
    image: nginx:alpine
    container_name: celest-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - celest-api
      - celest-dashboard
    restart: unless-stopped
    networks:
      - celest-network
    profiles:
      - production

volumes:
  mlflow-data:
    driver: local

networks:
  celest-network:
    driver: bridge

# Development override
# Use: docker-compose -f docker-compose.yml -f docker-compose.dev.yml up
---
# docker-compose.dev.yml
version: '3.8'

services:
  celest-api:
    build:
      target: builder
    volumes:
      - .:/app
      - /app/src/__pycache__
    environment:
      - DEBUG=true
      - RELOAD=true
    command: uvicorn src.api:app --host 0.0.0.0 --port 8000 --reload
    
  celest-dashboard:
    volumes:
      - .:/app
    environment:
      - DEBUG=true
