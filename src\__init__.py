"""
CELEST AI: Prototype for Geo-Effective CME Detection

This package contains the core modules for the CELEST AI system:
- Physics-Driven Consensus Engine (PDCE) for labeling
- Data preprocessing and feature engineering
- PatchTST model training and inference
- FastAPI service for real-time alerts
- Streamlit dashboard for operations
"""

__version__ = "0.1.0"
__author__ = "CELEST AI Development Team"
