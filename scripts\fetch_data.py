#!/usr/bin/env python3
"""
Data Acquisition Script for CELEST AI

This script downloads and prepares the necessary datasets for training
the CELEST AI CME detection system:

1. OMNIWeb 1-minute resolution solar wind data (2000-2020)
2. Richardson & Cane ICME catalog
3. Additional space weather indices (optional)

The script handles data validation, cleaning, and initial preprocessing
to prepare datasets for the PDCE labeling and model training pipeline.

Usage:
    python scripts/fetch_data.py --start-year 2000 --end-year 2020
    python scripts/fetch_data.py --quick-test  # Download 1 year for testing
"""

import argparse
import logging
import sys
from pathlib import Path
import pandas as pd
import numpy as np
import requests
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Tuple
import time
import os

# Add src to path for imports
sys.path.append(str(Path(__file__).parent.parent / "src"))

from preprocessing import OMNIWebDataProcessor, ICMECatalogProcessor

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/data_acquisition.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class DataAcquisitionPipeline:
    """
    Complete data acquisition pipeline for CELEST AI
    
    Coordinates the download and preprocessing of all required datasets
    for the CME detection system.
    """
    
    def __init__(self, data_dir: str = "data", force_download: bool = False):
        """
        Initialize the data acquisition pipeline
        
        Args:
            data_dir: Base directory for data storage
            force_download: Whether to re-download existing files
        """
        self.data_dir = Path(data_dir)
        self.raw_dir = self.data_dir / "raw"
        self.processed_dir = self.data_dir / "processed"
        self.force_download = force_download
        
        # Create directories
        self.raw_dir.mkdir(parents=True, exist_ok=True)
        self.processed_dir.mkdir(parents=True, exist_ok=True)
        
        # Create logs directory
        Path("logs").mkdir(exist_ok=True)
        
        # Initialize processors
        self.omniweb_processor = OMNIWebDataProcessor(str(self.raw_dir))
        self.icme_processor = ICMECatalogProcessor(str(self.raw_dir))
        
        logger.info(f"Initialized data acquisition pipeline")
        logger.info(f"Raw data directory: {self.raw_dir}")
        logger.info(f"Processed data directory: {self.processed_dir}")
    
    def run_full_pipeline(self, start_year: int, end_year: int) -> Dict[str, str]:
        """
        Run the complete data acquisition pipeline
        
        Args:
            start_year: Starting year for data acquisition
            end_year: Ending year for data acquisition
            
        Returns:
            Dictionary with paths to acquired datasets
        """
        logger.info(f"Starting full data acquisition pipeline for {start_year}-{end_year}")
        
        results = {}
        
        try:
            # Step 1: Download OMNIWeb data
            logger.info("Step 1: Acquiring OMNIWeb solar wind data...")
            omniweb_data = self.acquire_omniweb_data(start_year, end_year)
            omniweb_path = self.processed_dir / f"omniweb_data_{start_year}_{end_year}.parquet"
            omniweb_data.to_parquet(omniweb_path)
            results['omniweb_data'] = str(omniweb_path)
            logger.info(f"✅ OMNIWeb data saved to {omniweb_path}")
            
            # Step 2: Download ICME catalog
            logger.info("Step 2: Acquiring ICME catalog...")
            icme_catalog = self.acquire_icme_catalog()
            icme_path = self.processed_dir / "icme_catalog.parquet"
            icme_catalog.to_parquet(icme_path)
            results['icme_catalog'] = str(icme_path)
            logger.info(f"✅ ICME catalog saved to {icme_path}")
            
            # Step 3: Create labeled training dataset
            logger.info("Step 3: Creating labeled training dataset...")
            training_data = self.create_training_dataset(omniweb_data, icme_catalog)
            training_path = self.processed_dir / f"training_data_{start_year}_{end_year}.parquet"
            training_data.to_parquet(training_path)
            results['training_data'] = str(training_path)
            logger.info(f"✅ Training dataset saved to {training_path}")
            
            # Step 4: Generate data quality report
            logger.info("Step 4: Generating data quality report...")
            quality_report = self.generate_quality_report(omniweb_data, icme_catalog, training_data)
            report_path = self.processed_dir / f"data_quality_report_{start_year}_{end_year}.json"
            
            import json
            with open(report_path, 'w') as f:
                json.dump(quality_report, f, indent=2, default=str)
            results['quality_report'] = str(report_path)
            logger.info(f"✅ Quality report saved to {report_path}")
            
            # Step 5: Create data summary
            summary = self.create_data_summary(results, quality_report)
            summary_path = self.processed_dir / f"data_summary_{start_year}_{end_year}.txt"
            with open(summary_path, 'w') as f:
                f.write(summary)
            results['summary'] = str(summary_path)
            
            logger.info("🎉 Data acquisition pipeline completed successfully!")
            logger.info(f"Results: {results}")
            
            return results
            
        except Exception as e:
            logger.error(f"❌ Pipeline failed: {e}")
            raise
    
    def acquire_omniweb_data(self, start_year: int, end_year: int) -> pd.DataFrame:
        """Acquire and process OMNIWeb data"""
        
        logger.info(f"Downloading OMNIWeb data for {start_year}-{end_year}")
        
        # Use the OMNIWeb processor to load data
        omniweb_data = self.omniweb_processor.load_omniweb_data(
            start_year, end_year, force_download=self.force_download
        )
        
        logger.info(f"Loaded {len(omniweb_data)} OMNIWeb data points")
        logger.info(f"Date range: {omniweb_data['timestamp'].min()} to {omniweb_data['timestamp'].max()}")
        
        return omniweb_data
    
    def acquire_icme_catalog(self) -> pd.DataFrame:
        """Acquire ICME catalog data"""
        
        logger.info("Downloading ICME catalog")
        
        # Use the ICME processor to load catalog
        icme_catalog = self.icme_processor.load_icme_catalog(force_download=self.force_download)
        
        logger.info(f"Loaded {len(icme_catalog)} ICME events")
        if len(icme_catalog) > 0:
            logger.info(f"Event date range: {icme_catalog['start_time'].min()} to {icme_catalog['end_time'].max()}")
        
        return icme_catalog
    
    def create_training_dataset(self, omniweb_data: pd.DataFrame, 
                              icme_catalog: pd.DataFrame) -> pd.DataFrame:
        """Create labeled training dataset"""
        
        from preprocessing import create_training_dataset, add_ml_features
        from pdce import PhysicsDrivenConsensusEngine, PDCEConfig
        
        logger.info("Creating labeled training dataset...")
        
        # Create initial training dataset with ICME catalog labels
        training_data = create_training_dataset(
            omniweb_data, 
            icme_catalog, 
            output_path=""  # Don't save yet, we'll save later
        )
        
        # Apply PDCE labeling for physics-based labels
        logger.info("Applying Physics-Driven Consensus Engine labeling...")
        pdce_config = PDCEConfig(
            bz_threshold=-5.0,
            b_total_threshold=10.0,
            speed_threshold=450.0,
            event_window_hours=2,
            min_duration_minutes=30
        )
        
        pdce = PhysicsDrivenConsensusEngine(pdce_config)
        training_data = pdce.generate_labels_heuristic(training_data)
        
        # Validate PDCE results
        pdce_metrics = pdce.validate_labels(training_data)
        logger.info(f"PDCE validation metrics: {pdce_metrics}")
        
        logger.info(f"Training dataset created with {len(training_data)} samples")
        logger.info(f"ICME labels: {training_data['icme_label'].sum()} positive")
        logger.info(f"PDCE labels: {training_data['event_label'].sum()} positive")
        
        return training_data
    
    def generate_quality_report(self, omniweb_data: pd.DataFrame, 
                              icme_catalog: pd.DataFrame,
                              training_data: pd.DataFrame) -> Dict:
        """Generate comprehensive data quality report"""
        
        logger.info("Generating data quality report...")
        
        report = {
            'generation_time': datetime.now(),
            'omniweb_data': self._analyze_omniweb_quality(omniweb_data),
            'icme_catalog': self._analyze_icme_quality(icme_catalog),
            'training_data': self._analyze_training_quality(training_data),
            'data_alignment': self._analyze_data_alignment(omniweb_data, icme_catalog)
        }
        
        return report
    
    def _analyze_omniweb_quality(self, data: pd.DataFrame) -> Dict:
        """Analyze OMNIWeb data quality"""
        
        analysis = {
            'total_records': len(data),
            'date_range': {
                'start': data['timestamp'].min(),
                'end': data['timestamp'].max(),
                'duration_days': (data['timestamp'].max() - data['timestamp'].min()).days
            },
            'missing_data': {},
            'value_ranges': {},
            'data_gaps': []
        }
        
        # Analyze missing data
        key_columns = ['Bz_gsm', 'B_total', 'speed', 'density', 'temperature']
        for col in key_columns:
            if col in data.columns:
                missing_pct = data[col].isna().mean() * 100
                analysis['missing_data'][col] = {
                    'missing_count': data[col].isna().sum(),
                    'missing_percentage': missing_pct
                }
        
        # Analyze value ranges
        for col in key_columns:
            if col in data.columns and not data[col].isna().all():
                analysis['value_ranges'][col] = {
                    'min': float(data[col].min()),
                    'max': float(data[col].max()),
                    'mean': float(data[col].mean()),
                    'std': float(data[col].std())
                }
        
        # Detect data gaps (> 10 minutes)
        if 'timestamp' in data.columns:
            time_diffs = data['timestamp'].diff()
            large_gaps = time_diffs[time_diffs > timedelta(minutes=10)]
            
            for idx, gap in large_gaps.items():
                analysis['data_gaps'].append({
                    'start_time': data.loc[idx-1, 'timestamp'],
                    'end_time': data.loc[idx, 'timestamp'],
                    'duration_hours': gap.total_seconds() / 3600
                })
        
        return analysis
    
    def _analyze_icme_quality(self, catalog: pd.DataFrame) -> Dict:
        """Analyze ICME catalog quality"""
        
        if len(catalog) == 0:
            return {'total_events': 0, 'error': 'No ICME events found'}
        
        analysis = {
            'total_events': len(catalog),
            'date_range': {
                'start': catalog['start_time'].min(),
                'end': catalog['end_time'].max()
            },
            'duration_statistics': {
                'mean_hours': catalog['duration_hours'].mean(),
                'median_hours': catalog['duration_hours'].median(),
                'min_hours': catalog['duration_hours'].min(),
                'max_hours': catalog['duration_hours'].max()
            },
            'events_per_year': {}
        }
        
        # Events per year
        catalog['year'] = catalog['start_time'].dt.year
        events_per_year = catalog['year'].value_counts().sort_index()
        analysis['events_per_year'] = events_per_year.to_dict()
        
        return analysis
    
    def _analyze_training_quality(self, training_data: pd.DataFrame) -> Dict:
        """Analyze training dataset quality"""
        
        analysis = {
            'total_samples': len(training_data),
            'label_distribution': {
                'icme_positive': int(training_data['icme_label'].sum()),
                'icme_negative': int((training_data['icme_label'] == 0).sum()),
                'pdce_positive': int(training_data['event_label'].sum()),
                'pdce_negative': int((training_data['event_label'] == 0).sum())
            },
            'feature_statistics': {},
            'class_balance': {}
        }
        
        # Calculate class balance
        icme_pos_rate = training_data['icme_label'].mean()
        pdce_pos_rate = training_data['event_label'].mean()
        
        analysis['class_balance'] = {
            'icme_positive_rate': icme_pos_rate,
            'icme_imbalance_ratio': (1 - icme_pos_rate) / icme_pos_rate if icme_pos_rate > 0 else float('inf'),
            'pdce_positive_rate': pdce_pos_rate,
            'pdce_imbalance_ratio': (1 - pdce_pos_rate) / pdce_pos_rate if pdce_pos_rate > 0 else float('inf')
        }
        
        # Feature statistics
        numeric_columns = training_data.select_dtypes(include=[np.number]).columns
        for col in numeric_columns:
            if not training_data[col].isna().all():
                analysis['feature_statistics'][col] = {
                    'mean': float(training_data[col].mean()),
                    'std': float(training_data[col].std()),
                    'missing_rate': float(training_data[col].isna().mean())
                }
        
        return analysis
    
    def _analyze_data_alignment(self, omniweb_data: pd.DataFrame, 
                              icme_catalog: pd.DataFrame) -> Dict:
        """Analyze alignment between OMNIWeb data and ICME catalog"""
        
        if len(icme_catalog) == 0:
            return {'error': 'No ICME events to analyze alignment'}
        
        analysis = {
            'temporal_overlap': {},
            'coverage_statistics': {}
        }
        
        # Check temporal overlap
        omniweb_start = omniweb_data['timestamp'].min()
        omniweb_end = omniweb_data['timestamp'].max()
        icme_start = icme_catalog['start_time'].min()
        icme_end = icme_catalog['end_time'].max()
        
        analysis['temporal_overlap'] = {
            'omniweb_range': [omniweb_start, omniweb_end],
            'icme_range': [icme_start, icme_end],
            'overlap_start': max(omniweb_start, icme_start),
            'overlap_end': min(omniweb_end, icme_end)
        }
        
        # Calculate coverage statistics
        events_in_range = icme_catalog[
            (icme_catalog['start_time'] >= omniweb_start) & 
            (icme_catalog['end_time'] <= omniweb_end)
        ]
        
        analysis['coverage_statistics'] = {
            'total_icme_events': len(icme_catalog),
            'events_in_omniweb_range': len(events_in_range),
            'coverage_percentage': len(events_in_range) / len(icme_catalog) * 100
        }
        
        return analysis
    
    def create_data_summary(self, results: Dict[str, str], quality_report: Dict) -> str:
        """Create human-readable data summary"""
        
        summary = f"""
CELEST AI Data Acquisition Summary
Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

=== DATASETS ACQUIRED ===
"""
        
        for dataset_name, file_path in results.items():
            if dataset_name != 'summary':
                summary += f"• {dataset_name}: {file_path}\n"
        
        summary += f"""
=== DATA QUALITY OVERVIEW ===

OMNIWeb Data:
• Total records: {quality_report['omniweb_data']['total_records']:,}
• Date range: {quality_report['omniweb_data']['date_range']['start']} to {quality_report['omniweb_data']['date_range']['end']}
• Duration: {quality_report['omniweb_data']['date_range']['duration_days']} days

ICME Catalog:
• Total events: {quality_report['icme_catalog']['total_events']}
• Average duration: {quality_report['icme_catalog'].get('duration_statistics', {}).get('mean_hours', 'N/A')} hours

Training Dataset:
• Total samples: {quality_report['training_data']['total_samples']:,}
• ICME positive labels: {quality_report['training_data']['label_distribution']['icme_positive']:,}
• PDCE positive labels: {quality_report['training_data']['label_distribution']['pdce_positive']:,}
• ICME positive rate: {quality_report['training_data']['class_balance']['icme_positive_rate']:.3f}
• PDCE positive rate: {quality_report['training_data']['class_balance']['pdce_positive_rate']:.3f}

=== NEXT STEPS ===
1. Review the data quality report for any issues
2. Run model training: python src/model_train.py --data {results.get('training_data', '')}
3. Start the API server: uvicorn src.api:app --reload
4. Launch the dashboard: streamlit run src/dashboard.py

For detailed analysis, see the quality report: {results.get('quality_report', '')}
"""
        
        return summary


def main():
    """Main function for the data acquisition script"""
    
    parser = argparse.ArgumentParser(description='CELEST AI Data Acquisition Pipeline')
    parser.add_argument('--start-year', type=int, default=2010,
                       help='Starting year for data acquisition (default: 2010)')
    parser.add_argument('--end-year', type=int, default=2012,
                       help='Ending year for data acquisition (default: 2012)')
    parser.add_argument('--data-dir', type=str, default='data',
                       help='Base directory for data storage (default: data)')
    parser.add_argument('--force-download', action='store_true',
                       help='Force re-download of existing files')
    parser.add_argument('--quick-test', action='store_true',
                       help='Quick test mode: download only 2010-2011 data')
    
    args = parser.parse_args()
    
    # Quick test mode
    if args.quick_test:
        args.start_year = 2010
        args.end_year = 2011
        logger.info("Quick test mode: downloading 2010-2011 data only")
    
    # Validate year range
    current_year = datetime.now().year
    if args.start_year < 1995 or args.end_year > current_year:
        logger.error(f"Year range must be between 1995 and {current_year}")
        sys.exit(1)
    
    if args.start_year > args.end_year:
        logger.error("Start year must be <= end year")
        sys.exit(1)
    
    try:
        # Initialize and run pipeline
        pipeline = DataAcquisitionPipeline(
            data_dir=args.data_dir,
            force_download=args.force_download
        )
        
        results = pipeline.run_full_pipeline(args.start_year, args.end_year)
        
        # Print summary
        if 'summary' in results:
            with open(results['summary'], 'r') as f:
                print(f.read())
        
        logger.info("✅ Data acquisition completed successfully!")
        
    except KeyboardInterrupt:
        logger.info("❌ Data acquisition interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"❌ Data acquisition failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
