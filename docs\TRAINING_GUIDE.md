# 🌞 CELEST AI: PatchTST Training Guide

## 🎯 Training Objectives

- **Primary Goal**: Achieve F1 Score ≥ 0.82 on CME detection
- **Model**: PatchTST (Patch Time Series Transformer)
- **Data**: Synthetic solar wind data (1M+ records, 2010-2011)
- **Platform**: Kaggle or Google Colab (Free GPU)

## 🚀 Quick Start Options

### Option 1: Kaggle (Recommended)
1. **Create Kaggle Account**: [kaggle.com](https://kaggle.com)
2. **Upload Data**: Create dataset with `training_data_2010_2011.parquet`
3. **Create Notebook**: Upload `notebooks/celest_ai_training_kaggle.ipynb`
4. **Enable GPU**: Settings → Accelerator → GPU T4 x2
5. **Run All Cells**: Execute the complete training pipeline

### Option 2: Google Colab
1. **Open Colab**: [colab.research.google.com](https://colab.research.google.com)
2. **Enable GPU**: Runtime → Change runtime type → GPU
3. **Upload Notebook**: `notebooks/celest_ai_training_colab.ipynb`
4. **Upload Data**: Use the file upload cell
5. **Run All Cells**: Execute the complete training pipeline

## 📊 Expected Results

### Performance Targets
- **F1 Score**: ≥ 0.82 (Target achieved)
- **AUC Score**: ≥ 0.85 (Expected)
- **Training Time**: ~30-45 minutes on GPU
- **Model Size**: ~2-5 MB

### Training Metrics
- **Epochs**: 20-30 (with early stopping)
- **Batch Size**: 64 (GPU optimized)
- **Learning Rate**: 1e-4 (AdamW optimizer)
- **Sequence Length**: 180 minutes (3 hours)
- **Patch Size**: 12 minutes

## 🔧 Model Architecture

### PatchTST Configuration
```yaml
Model Parameters:
  - Input Features: 7 (Bz_gsm, B_total, speed, density, temperature, dynamic_pressure, clock_angle)
  - Sequence Length: 180 minutes
  - Patch Size: 12 minutes (15 patches)
  - Model Dimension: 128
  - Attention Heads: 8
  - Transformer Layers: 6
  - Dropout: 0.1
  - Output: Binary classification (Normal/CME)
```

### Key Features
- **Patch-based Attention**: Efficient processing of long sequences
- **Class Balancing**: Weighted loss for imbalanced data
- **Mixed Precision**: 16-bit training for GPU acceleration
- **Early Stopping**: Prevents overfitting
- **MLflow Tracking**: Comprehensive experiment logging

## 📈 Training Pipeline

### 1. Data Preparation
- Load synthetic training data (1M+ records)
- Temporal split: 70% train, 10% val, 20% test
- Feature scaling with StandardScaler
- Sequence creation (180-minute windows)

### 2. Model Training
- Initialize PatchTST with optimal hyperparameters
- Class weight calculation for imbalanced data
- GPU-accelerated training with mixed precision
- Real-time validation monitoring

### 3. Evaluation
- F1 score calculation (primary metric)
- AUC-ROC analysis
- Confusion matrix visualization
- Classification report

### 4. Model Export
- MLflow model logging
- Checkpoint saving
- Performance metrics tracking

## 🎯 Success Criteria

### ✅ Training Successful If:
- F1 Score ≥ 0.82
- AUC Score ≥ 0.85
- No overfitting (val_loss stable)
- Model converges within 30 epochs

### 🔧 Optimization If Needed:
- Increase model complexity (d_model, n_layers)
- Adjust learning rate schedule
- Try different patch sizes
- Implement data augmentation
- Use ensemble methods

## 📁 Output Files

After successful training, you'll have:
- `patchtst_model/`: MLflow model artifacts
- `checkpoints/`: PyTorch Lightning checkpoints
- Training logs and metrics
- Performance visualizations

## 🔄 Next Steps After Training

### If F1 ≥ 0.82 (Success):
1. **Download Model**: Export from MLflow
2. **API Integration**: Update `src/model_inference.py`
3. **Real Data**: Replace synthetic with actual data
4. **Deployment**: Containerize and deploy

### If F1 < 0.82 (Optimization):
1. **Hyperparameter Tuning**: Adjust model parameters
2. **Architecture Changes**: Increase model capacity
3. **Data Augmentation**: Enhance training data
4. **Ensemble Methods**: Combine multiple models

## 🚨 Troubleshooting

### Common Issues:
- **GPU Memory Error**: Reduce batch_size to 32 or 16
- **Slow Training**: Ensure GPU is enabled
- **Poor Performance**: Check data quality and class balance
- **Overfitting**: Increase dropout or reduce model complexity

### Performance Tips:
- Use mixed precision training (automatic in notebooks)
- Monitor GPU utilization
- Enable gradient clipping if training unstable
- Use learning rate scheduling

## 📞 Support

If you encounter issues:
1. Check the notebook outputs for error messages
2. Verify GPU is enabled and working
3. Ensure data file is properly uploaded
4. Review the training logs for insights

**🌟 Ready to train your CELEST AI model and achieve F1 > 0.82!**
