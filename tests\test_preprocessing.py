"""
Test suite for preprocessing module

This module contains unit tests for the data preprocessing functionality
including OMNIWeb data processing, ICME catalog handling, and feature engineering.
"""

import pytest
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from pathlib import Path
import tempfile
import shutil
import sys

# Add src to path for imports
sys.path.append(str(Path(__file__).parent.parent / "src"))

from preprocessing import (
    OMNIWebDataProcessor, 
    ICMECatalogProcessor, 
    create_training_dataset,
    add_ml_features
)


class TestOMNIWebDataProcessor:
    """Test cases for OMNIWeb data processor"""
    
    @pytest.fixture
    def temp_data_dir(self):
        """Create temporary directory for test data"""
        temp_dir = tempfile.mkdtemp()
        yield temp_dir
        shutil.rmtree(temp_dir)
    
    @pytest.fixture
    def processor(self, temp_data_dir):
        """Create OMNIWeb processor instance"""
        return OMNIWebDataProcessor(temp_data_dir)
    
    @pytest.fixture
    def sample_omniweb_data(self):
        """Create sample OMNIWeb data for testing"""
        np.random.seed(42)
        n_points = 1000
        
        data = pd.DataFrame({
            'timestamp': pd.date_range('2020-01-01', periods=n_points, freq='1min'),
            'Bz_gsm': np.random.normal(-1, 3, n_points),
            'B_total': np.random.normal(8, 2, n_points),
            'speed': np.random.normal(400, 40, n_points),
            'density': np.random.normal(5, 1.5, n_points),
            'temperature': np.random.normal(100000, 15000, n_points)
        })
        
        # Add some missing values
        data.loc[100:110, 'Bz_gsm'] = np.nan
        data.loc[200:205, 'speed'] = np.nan
        
        # Add some invalid values
        data.loc[300, 'speed'] = -100  # Invalid negative speed
        data.loc[301, 'B_total'] = -5   # Invalid negative field magnitude
        
        return data
    
    def test_processor_initialization(self, temp_data_dir):
        """Test processor initialization"""
        processor = OMNIWebDataProcessor(temp_data_dir)
        
        assert processor.data_dir == Path(temp_data_dir)
        assert processor.data_dir.exists()
    
    def test_clean_omniweb_data(self, processor, sample_omniweb_data):
        """Test data cleaning functionality"""
        # Create raw data with year, day, hour, minute columns
        raw_data = sample_omniweb_data.copy()
        raw_data['year'] = raw_data['timestamp'].dt.year
        raw_data['day'] = raw_data['timestamp'].dt.dayofyear
        raw_data['hour'] = raw_data['timestamp'].dt.hour
        raw_data['minute'] = raw_data['timestamp'].dt.minute
        raw_data = raw_data.drop('timestamp', axis=1)
        
        # Clean the data
        cleaned_data = processor._clean_omniweb_data(raw_data)
        
        # Verify timestamp creation
        assert 'timestamp' in cleaned_data.columns
        assert cleaned_data['timestamp'].dtype == 'datetime64[ns]'
        
        # Verify data is sorted by timestamp
        assert cleaned_data['timestamp'].is_monotonic_increasing
        
        # Verify invalid values are handled
        assert (cleaned_data['speed'] >= 200).all()  # Minimum valid speed
        assert (cleaned_data['B_total'] >= 0).all()   # Non-negative field magnitude
    
    def test_remove_invalid_values(self, processor, sample_omniweb_data):
        """Test invalid value removal"""
        data_with_invalid = sample_omniweb_data.copy()
        
        # Add clearly invalid values
        data_with_invalid.loc[0, 'speed'] = 2000  # Too high
        data_with_invalid.loc[1, 'density'] = -5  # Negative
        data_with_invalid.loc[2, 'Bz_gsm'] = 100  # Too high
        
        cleaned_data = processor._remove_invalid_values(data_with_invalid)
        
        # Check that invalid values are replaced with NaN
        assert pd.isna(cleaned_data.loc[0, 'speed'])
        assert pd.isna(cleaned_data.loc[1, 'density'])
        assert pd.isna(cleaned_data.loc[2, 'Bz_gsm'])
    
    def test_interpolate_missing_values(self, processor, sample_omniweb_data):
        """Test missing value interpolation"""
        data_with_gaps = sample_omniweb_data.copy()
        
        # Create a small gap
        data_with_gaps.loc[500:505, 'Bz_gsm'] = np.nan
        
        interpolated_data = processor._interpolate_missing_values(data_with_gaps)
        
        # Check that small gaps are filled
        assert not interpolated_data.loc[500:505, 'Bz_gsm'].isna().any()
    
    def test_add_derived_features(self, processor, sample_omniweb_data):
        """Test derived feature calculation"""
        enhanced_data = processor._add_derived_features(sample_omniweb_data)
        
        # Check that derived features are added
        expected_features = ['dynamic_pressure', 'hour', 'day_of_year']
        for feature in expected_features:
            assert feature in enhanced_data.columns
        
        # Verify dynamic pressure calculation
        expected_dyn_pressure = (1.67e-6 * 
                               enhanced_data['density'] * 
                               enhanced_data['speed']**2)
        np.testing.assert_array_almost_equal(
            enhanced_data['dynamic_pressure'], 
            expected_dyn_pressure, 
            decimal=10
        )


class TestICMECatalogProcessor:
    """Test cases for ICME catalog processor"""
    
    @pytest.fixture
    def temp_data_dir(self):
        """Create temporary directory for test data"""
        temp_dir = tempfile.mkdtemp()
        yield temp_dir
        shutil.rmtree(temp_dir)
    
    @pytest.fixture
    def processor(self, temp_data_dir):
        """Create ICME catalog processor instance"""
        return ICMECatalogProcessor(temp_data_dir)
    
    @pytest.fixture
    def sample_icme_catalog(self):
        """Create sample ICME catalog for testing"""
        events = []
        base_date = datetime(2020, 1, 1)
        
        for i in range(5):
            start_time = base_date + timedelta(days=i*30)
            duration = 12 + i * 6  # 12, 18, 24, 30, 36 hours
            end_time = start_time + timedelta(hours=duration)
            
            events.append({
                'start_time': start_time,
                'end_time': end_time,
                'duration_hours': duration,
                'quality': str((i % 3) + 1)
            })
        
        return pd.DataFrame(events)
    
    def test_processor_initialization(self, temp_data_dir):
        """Test processor initialization"""
        processor = ICMECatalogProcessor(temp_data_dir)
        
        assert processor.data_dir == Path(temp_data_dir)
        assert processor.data_dir.exists()
    
    def test_parse_icme_catalog(self, processor, sample_icme_catalog, temp_data_dir):
        """Test ICME catalog parsing"""
        # Save sample catalog to file
        catalog_file = Path(temp_data_dir) / "test_catalog.csv"
        sample_icme_catalog.to_csv(catalog_file, index=False)
        
        # Parse the catalog
        parsed_catalog = processor._parse_icme_catalog(catalog_file)
        
        # Verify parsing
        assert len(parsed_catalog) == len(sample_icme_catalog)
        assert 'start_time' in parsed_catalog.columns
        assert 'end_time' in parsed_catalog.columns
        assert parsed_catalog['start_time'].dtype == 'datetime64[ns]'
        assert parsed_catalog['end_time'].dtype == 'datetime64[ns]'


class TestFeatureEngineering:
    """Test cases for feature engineering functions"""
    
    @pytest.fixture
    def sample_data(self):
        """Create sample data for feature engineering tests"""
        np.random.seed(42)
        n_points = 500
        
        return pd.DataFrame({
            'timestamp': pd.date_range('2020-01-01', periods=n_points, freq='1min'),
            'Bz_gsm': np.random.normal(-1, 3, n_points),
            'B_total': np.random.normal(8, 2, n_points),
            'speed': np.random.normal(400, 40, n_points),
            'density': np.random.normal(5, 1.5, n_points)
        })
    
    def test_add_ml_features(self, sample_data):
        """Test ML feature addition"""
        enhanced_data = add_ml_features(sample_data)
        
        # Check that new features are added
        original_cols = set(sample_data.columns)
        new_cols = set(enhanced_data.columns) - original_cols
        
        assert len(new_cols) > 0, "No new features were added"
        
        # Check for specific feature types
        rolling_features = [col for col in new_cols if '_mean_' in col or '_std_' in col]
        gradient_features = [col for col in new_cols if '_gradient' in col]
        lag_features = [col for col in new_cols if '_lag_' in col]
        
        assert len(rolling_features) > 0, "No rolling features added"
        assert len(gradient_features) > 0, "No gradient features added"
        assert len(lag_features) > 0, "No lag features added"
        
        # Verify rolling mean calculation for a specific feature
        if 'Bz_gsm_mean_10m' in enhanced_data.columns:
            expected_mean = sample_data['Bz_gsm'].rolling(window=10, center=True).mean()
            pd.testing.assert_series_equal(
                enhanced_data['Bz_gsm_mean_10m'], 
                expected_mean, 
                check_names=False
            )
    
    def test_create_training_dataset(self, sample_data):
        """Test training dataset creation"""
        # Create sample ICME catalog
        icme_catalog = pd.DataFrame({
            'start_time': [sample_data['timestamp'].iloc[100]],
            'end_time': [sample_data['timestamp'].iloc[200]],
            'duration_hours': [100],
            'quality': ['1']
        })
        
        # Create training dataset
        with tempfile.TemporaryDirectory() as temp_dir:
            output_path = Path(temp_dir) / "training_data.parquet"
            training_data = create_training_dataset(
                sample_data, icme_catalog, str(output_path)
            )
        
        # Verify dataset creation
        assert 'icme_label' in training_data.columns
        assert 'icme_event_id' in training_data.columns
        
        # Check that ICME event is properly labeled
        event_mask = (
            (training_data['timestamp'] >= icme_catalog['start_time'].iloc[0]) &
            (training_data['timestamp'] <= icme_catalog['end_time'].iloc[0])
        )
        assert training_data.loc[event_mask, 'icme_label'].all()
        
        # Check that non-event periods are labeled as 0
        non_event_mask = ~event_mask
        assert not training_data.loc[non_event_mask, 'icme_label'].any()


class TestDataValidation:
    """Test cases for data validation and quality checks"""
    
    def test_data_consistency(self):
        """Test data consistency checks"""
        # Create data with inconsistent magnetic field components
        data = pd.DataFrame({
            'Bx_gsm': [1, 2, 3],
            'By_gsm': [1, 2, 3], 
            'Bz_gsm': [1, 2, 3],
            'B_total': [1, 2, 3]  # Should be sqrt(Bx^2 + By^2 + Bz^2)
        })
        
        # Calculate expected B_total
        expected_b_total = np.sqrt(
            data['Bx_gsm']**2 + data['By_gsm']**2 + data['Bz_gsm']**2
        )
        
        # Check consistency (allowing for small numerical errors)
        consistency_check = np.abs(data['B_total'] - expected_b_total) < 0.1
        
        # This test should fail for our inconsistent data
        assert not consistency_check.all()
    
    def test_temporal_continuity(self):
        """Test temporal continuity of data"""
        # Create data with time gaps
        timestamps = pd.date_range('2020-01-01', periods=100, freq='1min')
        # Remove some timestamps to create gaps
        timestamps = timestamps.delete([50, 51, 52])  # 3-minute gap
        
        data = pd.DataFrame({
            'timestamp': timestamps,
            'value': range(len(timestamps))
        })
        
        # Check for gaps larger than expected (> 1 minute)
        time_diffs = data['timestamp'].diff()
        large_gaps = time_diffs > timedelta(minutes=1)
        
        assert large_gaps.sum() > 0, "Expected to find time gaps but none found"


if __name__ == "__main__":
    # Run tests if script is executed directly
    pytest.main([__file__, "-v"])
