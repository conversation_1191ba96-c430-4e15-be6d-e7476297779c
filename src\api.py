"""
FastAPI Application for CELEST AI

This module provides the REST API interface for the CELEST AI system.
It exposes endpoints for real-time CME detection and alerting.

Key endpoints:
- GET /: Health check and system status
- POST /api/v1/predict: Main prediction endpoint
- GET /api/v1/status: Current system status
- GET /api/v1/model/info: Model information and metrics
"""

from fastapi import FastAPI, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
from typing import List, Dict, Optional, Any
import pandas as pd
import numpy as np
import logging
from datetime import datetime, timedelta
import uvicorn
import os
import json

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize FastAPI app
app = FastAPI(
    title="CELEST AI Alerting API",
    description="Provides real-time alerts for geo-effective CMEs using L1 satellite data",
    version="0.1.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# Add CORS middleware for web dashboard integration
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, specify allowed origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Global variables for model and system state
model = None
model_info = {}
system_status = {
    "status": "initializing",
    "last_update": datetime.now(),
    "model_loaded": False,
    "total_predictions": 0,
    "alerts_issued": 0
}


# Pydantic models for API requests and responses
class SolarWindDataPoint(BaseModel):
    """Single data point of solar wind measurements"""
    timestamp: datetime
    Bz_gsm: float = Field(..., description="Bz component in GSM coordinates (nT)")
    B_total: float = Field(..., description="Total magnetic field magnitude (nT)")
    speed: float = Field(..., description="Solar wind speed (km/s)")
    density: float = Field(..., description="Proton density (p/cc)")
    temperature: Optional[float] = Field(None, description="Proton temperature (K)")
    
    class Config:
        schema_extra = {
            "example": {
                "timestamp": "2023-01-01T12:00:00Z",
                "Bz_gsm": -3.2,
                "B_total": 8.5,
                "speed": 420.0,
                "density": 6.8,
                "temperature": 95000.0
            }
        }


class TimeSeriesData(BaseModel):
    """Time series of solar wind data for prediction"""
    data: List[SolarWindDataPoint] = Field(..., description="List of solar wind measurements")
    
    class Config:
        schema_extra = {
            "example": {
                "data": [
                    {
                        "timestamp": "2023-01-01T12:00:00Z",
                        "Bz_gsm": -3.2,
                        "B_total": 8.5,
                        "speed": 420.0,
                        "density": 6.8,
                        "temperature": 95000.0
                    }
                ]
            }
        }


class AlertStatus(BaseModel):
    """Alert status response"""
    status: str = Field(..., description="Alert status: NORMAL, WATCH, or ALERT")
    confidence: float = Field(..., ge=0.0, le=1.0, description="Prediction confidence (0-1)")
    predicted_impact_in_minutes: int = Field(..., description="Predicted time to impact in minutes")
    drivers: List[str] = Field(..., description="Physical drivers of the alert")
    timestamp: datetime = Field(..., description="Timestamp of the prediction")
    model_version: str = Field(..., description="Version of the model used")
    
    class Config:
        schema_extra = {
            "example": {
                "status": "ALERT",
                "confidence": 0.84,
                "predicted_impact_in_minutes": 35,
                "drivers": ["Southward Bz excursion < -5nT", "Enhanced density > 15 p/cc"],
                "timestamp": "2023-01-01T12:00:00Z",
                "model_version": "PatchTST-v1.0"
            }
        }


class SystemStatus(BaseModel):
    """System status information"""
    status: str
    last_update: datetime
    model_loaded: bool
    total_predictions: int
    alerts_issued: int
    uptime_seconds: float


class ModelInfo(BaseModel):
    """Model information and performance metrics"""
    model_name: str
    model_version: str
    training_date: datetime
    performance_metrics: Dict[str, float]
    feature_importance: Optional[Dict[str, float]] = None


# Startup event to load the model
@app.on_event("startup")
async def startup_event():
    """Initialize the system and load the trained model"""
    global model, model_info, system_status
    
    logger.info("Starting CELEST AI API...")
    
    try:
        # Load the trained model (placeholder implementation)
        model = load_model()
        model_info = get_model_metadata()
        
        system_status.update({
            "status": "operational",
            "model_loaded": True,
            "last_update": datetime.now()
        })
        
        logger.info("CELEST AI API started successfully")
        
    except Exception as e:
        logger.error(f"Failed to start API: {e}")
        system_status.update({
            "status": "error",
            "model_loaded": False,
            "last_update": datetime.now()
        })


def load_model():
    """
    Load the trained PatchTST model from MLflow
    
    This is a placeholder implementation. In the real system,
    this would load the model from MLflow model registry.
    """
    logger.info("Loading trained model...")
    
    # Placeholder: Return a mock model object
    # In real implementation, this would use MLflow:
    # import mlflow.pytorch
    # model = mlflow.pytorch.load_model("models:/celest-ai-patchtst/Production")
    
    class MockModel:
        def predict(self, data):
            # Simple heuristic for demonstration
            if len(data) == 0:
                return {"status": "NORMAL", "confidence": 0.95, "impact_time": 0}
            
            # Check for CME-like conditions
            latest_data = data.iloc[-1]
            
            if latest_data['Bz_gsm'] < -5 and latest_data['speed'] > 450:
                return {
                    "status": "ALERT",
                    "confidence": 0.85,
                    "impact_time": 35,
                    "drivers": ["Strong southward Bz", "High solar wind speed"]
                }
            elif latest_data['Bz_gsm'] < -3 and latest_data['B_total'] > 10:
                return {
                    "status": "WATCH", 
                    "confidence": 0.65,
                    "impact_time": 60,
                    "drivers": ["Moderate southward Bz", "Enhanced magnetic field"]
                }
            else:
                return {
                    "status": "NORMAL",
                    "confidence": 0.92,
                    "impact_time": 0,
                    "drivers": []
                }
    
    return MockModel()


def get_model_metadata():
    """Get information about the loaded model"""
    return {
        "model_name": "PatchTST",
        "model_version": "v1.0-demo",
        "training_date": datetime(2023, 1, 1),
        "performance_metrics": {
            "f1_score": 0.82,
            "precision": 0.78,
            "recall": 0.86,
            "accuracy": 0.94
        }
    }


# API Endpoints
@app.get("/", response_model=Dict[str, str])
def root():
    """Health check endpoint"""
    return {
        "message": "CELEST AI API is running",
        "status": system_status["status"],
        "version": "0.1.0"
    }


@app.get("/api/v1/status", response_model=SystemStatus)
def get_system_status():
    """Get current system status"""
    global system_status

    # Calculate uptime
    uptime = (datetime.now() - system_status["last_update"]).total_seconds()

    return SystemStatus(
        status=system_status["status"],
        last_update=system_status["last_update"],
        model_loaded=system_status["model_loaded"],
        total_predictions=system_status["total_predictions"],
        alerts_issued=system_status["alerts_issued"],
        uptime_seconds=uptime
    )


@app.get("/api/v1/model/info", response_model=ModelInfo)
def get_model_info():
    """Get information about the loaded model"""
    if not system_status["model_loaded"]:
        raise HTTPException(status_code=503, detail="Model not loaded")

    return ModelInfo(**model_info)


@app.post("/api/v1/predict", response_model=AlertStatus)
def predict_cme_alert(ts_data: TimeSeriesData, background_tasks: BackgroundTasks):
    """
    Main prediction endpoint for CME alerts
    
    Analyzes a window of time-series solar wind data and returns an alert status.
    """
    global model, system_status
    
    if not system_status["model_loaded"]:
        raise HTTPException(status_code=503, detail="Model not loaded")
    
    try:
        # Convert input data to DataFrame
        data_dicts = [point.dict() for point in ts_data.data]
        input_df = pd.DataFrame(data_dicts)
        
        # Validate input data
        if len(input_df) == 0:
            raise HTTPException(status_code=400, detail="No data provided")
        
        required_columns = ['Bz_gsm', 'B_total', 'speed', 'density']
        missing_columns = [col for col in required_columns if col not in input_df.columns]
        if missing_columns:
            raise HTTPException(
                status_code=400, 
                detail=f"Missing required columns: {missing_columns}"
            )
        
        # Make prediction using the loaded model
        prediction = model.predict(input_df)
        
        # Update system statistics
        system_status["total_predictions"] += 1
        if prediction["status"] == "ALERT":
            system_status["alerts_issued"] += 1
        
        # Create response
        response = AlertStatus(
            status=prediction["status"],
            confidence=prediction["confidence"],
            predicted_impact_in_minutes=prediction["impact_time"],
            drivers=prediction["drivers"],
            timestamp=datetime.now(),
            model_version=model_info["model_version"]
        )
        
        # Log the prediction
        logger.info(f"Prediction made: {prediction['status']} (confidence: {prediction['confidence']:.2f})")
        
        # Schedule background task for logging/monitoring
        background_tasks.add_task(log_prediction, response)
        
        return response
        
    except Exception as e:
        logger.error(f"Prediction error: {e}")
        raise HTTPException(status_code=500, detail=f"Prediction failed: {str(e)}")


def log_prediction(prediction: AlertStatus):
    """Background task to log predictions for monitoring and analysis"""

    # In a real system, this would log to a database or monitoring system
    log_entry = {
        "timestamp": prediction.timestamp.isoformat(),
        "status": prediction.status,
        "confidence": prediction.confidence,
        "predicted_impact": prediction.predicted_impact_in_minutes,
        "drivers": prediction.drivers
    }

    # For now, just log to file
    log_file = "logs/predictions.jsonl"
    os.makedirs("logs", exist_ok=True)

    with open(log_file, "a") as f:
        f.write(json.dumps(log_entry) + "\n")


# Additional utility endpoints
@app.get("/api/v1/health")
def health_check():
    """Detailed health check for monitoring systems"""

    health_status = {
        "api_status": "healthy" if system_status["status"] == "operational" else "unhealthy",
        "model_status": "loaded" if system_status["model_loaded"] else "not_loaded",
        "timestamp": datetime.now(),
        "predictions_count": system_status["total_predictions"],
        "alerts_count": system_status["alerts_issued"]
    }

    return health_status


@app.get("/api/v1/metrics")
def get_metrics():
    """Get system metrics for monitoring"""

    return {
        "total_predictions": system_status["total_predictions"],
        "alerts_issued": system_status["alerts_issued"],
        "alert_rate": system_status["alerts_issued"] / max(1, system_status["total_predictions"]),
        "uptime_seconds": (datetime.now() - system_status["last_update"]).total_seconds(),
        "model_version": model_info.get("model_version", "unknown")
    }


if __name__ == "__main__":
    # Run the API server
    uvicorn.run(
        "api:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
