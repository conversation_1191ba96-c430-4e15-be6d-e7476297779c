name: celestai
channels:
  - conda-forge
  - pytorch
  - defaults
dependencies:
  - python=3.10
  - pip
  
  # Core data science packages
  - pandas>=1.5.0
  - numpy>=1.21.0
  - scipy>=1.9.0
  - scikit-learn>=1.1.0
  
  # Machine learning frameworks
  - pytorch>=1.13.0
  - pytorch-lightning>=1.8.0
  - transformers>=4.21.0
  
  # Time series and forecasting
  - statsmodels>=0.13.0
  
  # Visualization
  - matplotlib>=3.5.0
  - seaborn>=0.11.0
  - plotly>=5.10.0
  
  # Web frameworks
  - fastapi>=0.85.0
  - uvicorn>=0.18.0
  - streamlit>=1.12.0
  
  # Data handling
  - requests>=2.28.0
  - beautifulsoup4>=4.11.0
  - lxml>=4.9.0
  
  # Scientific computing
  - astropy>=5.1.0
  - sunpy>=4.0.0
  
  # Development tools
  - jupyter>=1.0.0
  - ipykernel>=6.15.0
  - black>=22.0.0
  - flake8>=5.0.0
  - pytest>=7.1.0
  
  # MLOps and experiment tracking
  - mlflow>=1.28.0
  
  # Explainable AI
  - shap>=0.41.0
  
  # Additional utilities
  - tqdm>=4.64.0
  - python-dotenv>=0.20.0
  - pydantic>=1.10.0
  
  # Pip-only packages
  - pip:
    - pytorch-forecasting>=0.10.0
    - pymc>=4.2.0
    - arviz>=0.12.0
    - dash>=2.6.0
    - dash-bootstrap-components>=1.2.0
