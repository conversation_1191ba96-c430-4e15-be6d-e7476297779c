"""
Model Training Module for CELEST AI

This module implements the training pipeline for the PatchTST transformer model
used for CME detection. It includes data preparation, model architecture,
training loop, and experiment tracking with MLflow.

Key components:
- PatchTST model implementation
- Training and validation loops
- MLflow experiment tracking
- Model evaluation and metrics
- Hyperparameter optimization
"""

import pandas as pd
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import pytorch_lightning as pl
from pytorch_lightning.callbacks import ModelCheckpoint, EarlyStopping
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import classification_report, confusion_matrix, roc_auc_score
import mlflow
import mlflow.pytorch
from typing import Dict, List, Tuple, Optional
import logging
from pathlib import Path
import yaml
import argparse
from datetime import datetime

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class CMEDataset(Dataset):
    """
    PyTorch Dataset for CME detection time series data
    """
    
    def __init__(self, data: pd.DataFrame, sequence_length: int = 180, 
                 target_column: str = 'event_label', feature_columns: List[str] = None):
        """
        Initialize the dataset
        
        Args:
            data: DataFrame with time series data
            sequence_length: Length of input sequences (in minutes)
            target_column: Name of the target column
            feature_columns: List of feature column names
        """
        self.data = data.sort_values('timestamp').reset_index(drop=True)
        self.sequence_length = sequence_length
        self.target_column = target_column
        
        if feature_columns is None:
            # Default feature columns
            self.feature_columns = [
                'Bz_gsm', 'B_total', 'speed', 'density', 'temperature',
                'dynamic_pressure', 'clock_angle'
            ]
        else:
            self.feature_columns = feature_columns
        
        # Filter available columns
        self.feature_columns = [col for col in self.feature_columns if col in data.columns]
        
        # Prepare sequences
        self.sequences, self.targets = self._create_sequences()
        
        logger.info(f"Created dataset with {len(self.sequences)} sequences")
        logger.info(f"Features: {self.feature_columns}")
        logger.info(f"Sequence length: {sequence_length} minutes")
    
    def _create_sequences(self) -> Tuple[np.ndarray, np.ndarray]:
        """Create sequences for time series prediction"""
        
        sequences = []
        targets = []
        
        for i in range(len(self.data) - self.sequence_length):
            # Extract sequence
            seq_data = self.data.iloc[i:i+self.sequence_length][self.feature_columns].values
            
            # Target is the label at the end of the sequence
            target = self.data.iloc[i+self.sequence_length][self.target_column]
            
            # Only include sequences with no missing values
            if not np.isnan(seq_data).any():
                sequences.append(seq_data)
                targets.append(target)
        
        return np.array(sequences), np.array(targets)
    
    def __len__(self):
        return len(self.sequences)
    
    def __getitem__(self, idx):
        return torch.FloatTensor(self.sequences[idx]), torch.LongTensor([self.targets[idx]])


class PatchTSTModel(pl.LightningModule):
    """
    PatchTST (Patch Time Series Transformer) implementation for CME detection
    
    This model uses patch-based attention to capture long-range dependencies
    in solar wind time series data.
    """
    
    def __init__(self, 
                 n_features: int,
                 sequence_length: int,
                 patch_size: int = 12,
                 d_model: int = 128,
                 n_heads: int = 8,
                 n_layers: int = 6,
                 dropout: float = 0.1,
                 learning_rate: float = 1e-4,
                 class_weights: Optional[torch.Tensor] = None):
        """
        Initialize PatchTST model
        
        Args:
            n_features: Number of input features
            sequence_length: Length of input sequences
            patch_size: Size of each patch
            d_model: Model dimension
            n_heads: Number of attention heads
            n_layers: Number of transformer layers
            dropout: Dropout rate
            learning_rate: Learning rate for optimization
            class_weights: Weights for class balancing
        """
        super().__init__()
        
        self.save_hyperparameters()
        
        self.n_features = n_features
        self.sequence_length = sequence_length
        self.patch_size = patch_size
        self.n_patches = sequence_length // patch_size
        self.d_model = d_model
        self.learning_rate = learning_rate
        
        # Patch embedding
        self.patch_embedding = nn.Linear(patch_size * n_features, d_model)
        
        # Positional encoding
        self.pos_encoding = nn.Parameter(torch.randn(1, self.n_patches, d_model))
        
        # Transformer encoder
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=d_model,
            nhead=n_heads,
            dim_feedforward=d_model * 4,
            dropout=dropout,
            batch_first=True
        )
        self.transformer = nn.TransformerEncoder(encoder_layer, num_layers=n_layers)
        
        # Classification head
        self.classifier = nn.Sequential(
            nn.LayerNorm(d_model),
            nn.Dropout(dropout),
            nn.Linear(d_model, d_model // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(d_model // 2, 2)  # Binary classification
        )
        
        # Loss function
        if class_weights is not None:
            self.criterion = nn.CrossEntropyLoss(weight=class_weights)
        else:
            self.criterion = nn.CrossEntropyLoss()
        
        # Metrics storage
        self.training_step_outputs = []
        self.validation_step_outputs = []
    
    def forward(self, x):
        """
        Forward pass
        
        Args:
            x: Input tensor of shape (batch_size, sequence_length, n_features)
        
        Returns:
            Logits for binary classification
        """
        batch_size = x.size(0)
        
        # Reshape to patches: (batch_size, n_patches, patch_size * n_features)
        x = x.view(batch_size, self.n_patches, self.patch_size * self.n_features)
        
        # Patch embedding
        x = self.patch_embedding(x)  # (batch_size, n_patches, d_model)
        
        # Add positional encoding
        x = x + self.pos_encoding
        
        # Transformer encoding
        x = self.transformer(x)  # (batch_size, n_patches, d_model)
        
        # Global average pooling
        x = x.mean(dim=1)  # (batch_size, d_model)
        
        # Classification
        logits = self.classifier(x)  # (batch_size, 2)
        
        return logits
    
    def training_step(self, batch, batch_idx):
        """Training step"""
        x, y = batch
        y = y.squeeze()
        
        logits = self(x)
        loss = self.criterion(logits, y)
        
        # Calculate accuracy
        preds = torch.argmax(logits, dim=1)
        acc = (preds == y).float().mean()
        
        # Log metrics
        self.log('train_loss', loss, on_step=True, on_epoch=True, prog_bar=True)
        self.log('train_acc', acc, on_step=True, on_epoch=True, prog_bar=True)
        
        self.training_step_outputs.append({'loss': loss, 'preds': preds, 'targets': y})
        
        return loss
    
    def validation_step(self, batch, batch_idx):
        """Validation step"""
        x, y = batch
        y = y.squeeze()
        
        logits = self(x)
        loss = self.criterion(logits, y)
        
        # Calculate accuracy
        preds = torch.argmax(logits, dim=1)
        acc = (preds == y).float().mean()
        
        # Get probabilities for AUC calculation
        probs = torch.softmax(logits, dim=1)[:, 1]
        
        # Log metrics
        self.log('val_loss', loss, on_epoch=True, prog_bar=True)
        self.log('val_acc', acc, on_epoch=True, prog_bar=True)
        
        self.validation_step_outputs.append({
            'loss': loss, 
            'preds': preds, 
            'targets': y, 
            'probs': probs
        })
        
        return loss
    
    def on_validation_epoch_end(self):
        """Calculate additional metrics at the end of validation epoch"""
        if not self.validation_step_outputs:
            return
        
        # Collect all predictions and targets
        all_preds = torch.cat([x['preds'] for x in self.validation_step_outputs])
        all_targets = torch.cat([x['targets'] for x in self.validation_step_outputs])
        all_probs = torch.cat([x['probs'] for x in self.validation_step_outputs])
        
        # Calculate AUC
        try:
            auc = roc_auc_score(all_targets.cpu().numpy(), all_probs.cpu().numpy())
            self.log('val_auc', auc, on_epoch=True, prog_bar=True)
        except ValueError:
            # Handle case where only one class is present
            pass
        
        # Clear outputs
        self.validation_step_outputs.clear()
    
    def configure_optimizers(self):
        """
        Configure optimizer and learning rate scheduler for stable training
        """
        # Define the optimizer with improved settings
        optimizer = optim.AdamW(
            self.parameters(),
            lr=self.learning_rate,
            weight_decay=1e-5
        )

        # Define the learning rate scheduler with improved parameters
        # It will watch 'val_loss' and reduce the LR if it plateaus
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            optimizer,
            mode='min',                    # We want to minimize the validation loss
            patience=3,                    # Wait 3 epochs with no improvement before reducing LR
            factor=0.5,                    # Reduce LR by half (e.g., 1e-5 -> 5e-6)
            verbose=True,                  # Print a message when the LR is reduced
            min_lr=1e-7                    # Don't reduce below this value
        )

        # Return the configuration for PyTorch Lightning
        return {
            'optimizer': optimizer,
            'lr_scheduler': {
                'scheduler': scheduler,
                'monitor': 'val_loss',     # The metric to monitor
                'interval': 'epoch',
                'frequency': 1,
            }
        }


def prepare_data(data_path: str, test_size: float = 0.2, val_size: float = 0.1) -> Tuple:
    """
    Prepare data for training
    
    Args:
        data_path: Path to the training data
        test_size: Fraction of data for testing
        val_size: Fraction of training data for validation
    
    Returns:
        Tuple of (train_loader, val_loader, test_loader, scaler, feature_columns)
    """
    logger.info(f"Loading data from {data_path}")
    
    # Load data
    if data_path.endswith('.parquet'):
        data = pd.read_parquet(data_path)
    else:
        data = pd.read_csv(data_path)
    
    # Select features for training
    feature_columns = [
        'Bz_gsm', 'B_total', 'speed', 'density', 'temperature',
        'dynamic_pressure', 'clock_angle'
    ]
    
    # Filter available columns
    available_features = [col for col in feature_columns if col in data.columns]
    logger.info(f"Using features: {available_features}")
    
    # Handle missing values
    data = data.dropna(subset=available_features + ['event_label'])
    
    # Split data temporally (important for time series)
    n_total = len(data)
    n_test = int(n_total * test_size)
    n_val = int((n_total - n_test) * val_size)
    
    train_data = data.iloc[:-n_test-n_val]
    val_data = data.iloc[-n_test-n_val:-n_test]
    test_data = data.iloc[-n_test:]
    
    logger.info(f"Data split: Train={len(train_data)}, Val={len(val_data)}, Test={len(test_data)}")
    
    # Scale features
    scaler = StandardScaler()
    train_data[available_features] = scaler.fit_transform(train_data[available_features])
    val_data[available_features] = scaler.transform(val_data[available_features])
    test_data[available_features] = scaler.transform(test_data[available_features])
    
    # Create datasets
    sequence_length = 180  # 3 hours of 1-minute data
    
    train_dataset = CMEDataset(train_data, sequence_length, feature_columns=available_features)
    val_dataset = CMEDataset(val_data, sequence_length, feature_columns=available_features)
    test_dataset = CMEDataset(test_data, sequence_length, feature_columns=available_features)
    
    # Create data loaders
    batch_size = 32
    
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, num_workers=0)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False, num_workers=0)
    test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False, num_workers=0)
    
    return train_loader, val_loader, test_loader, scaler, available_features


def train_model(config: Dict) -> pl.LightningModule:
    """
    Train the PatchTST model
    
    Args:
        config: Training configuration dictionary
    
    Returns:
        Trained model
    """
    logger.info("Starting model training...")
    
    # Prepare data
    train_loader, val_loader, test_loader, scaler, feature_columns = prepare_data(
        config['data_path'], 
        config.get('test_size', 0.2),
        config.get('val_size', 0.1)
    )
    
    # Calculate class weights for imbalanced data
    train_targets = []
    for batch in train_loader:
        _, targets = batch
        train_targets.extend(targets.squeeze().tolist())
    
    class_counts = np.bincount(train_targets)
    class_weights = torch.FloatTensor(len(class_counts) / class_counts)
    
    logger.info(f"Class distribution: {class_counts}")
    logger.info(f"Class weights: {class_weights}")
    
    # Initialize model
    model = PatchTSTModel(
        n_features=len(feature_columns),
        sequence_length=config.get('sequence_length', 180),
        patch_size=config.get('patch_size', 12),
        d_model=config.get('d_model', 128),
        n_heads=config.get('n_heads', 8),
        n_layers=config.get('n_layers', 6),
        dropout=config.get('dropout', 0.1),
        learning_rate=config.get('learning_rate', 1e-4),
        class_weights=class_weights
    )
    
    # Setup callbacks
    checkpoint_callback = ModelCheckpoint(
        monitor='val_loss',
        dirpath='models/checkpoints',
        filename='patchtst-{epoch:02d}-{val_loss:.2f}',
        save_top_k=3,
        mode='min'
    )
    
    early_stopping = EarlyStopping(
        monitor='val_loss',
        patience=10,
        mode='min'
    )
    
    # Setup trainer with improved stability settings
    trainer = pl.Trainer(
        max_epochs=config.get('max_epochs', 100),
        callbacks=[checkpoint_callback, early_stopping],
        accelerator='auto',
        devices='auto',
        log_every_n_steps=50,
        gradient_clip_val=1.0,          # Prevent gradient explosion
        deterministic=True              # For reproducible results
    )
    
    # Train model
    trainer.fit(model, train_loader, val_loader)
    
    # Test model
    trainer.test(model, test_loader)
    
    return model


def main():
    """Main training script"""
    parser = argparse.ArgumentParser(description='Train CELEST AI PatchTST model')
    parser.add_argument('--config', type=str, default='config/train_config.yaml',
                       help='Path to training configuration file')
    parser.add_argument('--data', type=str, default='data/processed/training_data.parquet',
                       help='Path to training data')
    
    args = parser.parse_args()
    
    # Load configuration
    if Path(args.config).exists():
        with open(args.config, 'r') as f:
            config = yaml.safe_load(f)
    else:
        # Default configuration
        config = {
            'data_path': args.data,
            'sequence_length': 180,
            'patch_size': 12,
            'd_model': 128,
            'n_heads': 8,
            'n_layers': 6,
            'dropout': 0.1,
            'learning_rate': 1e-4,
            'max_epochs': 100,
            'test_size': 0.2,
            'val_size': 0.1
        }
    
    # Start MLflow experiment
    mlflow.set_experiment("celest-ai-patchtst")
    
    with mlflow.start_run():
        # Log configuration
        mlflow.log_params(config)
        
        # Train model
        model = train_model(config)
        
        # Log model
        mlflow.pytorch.log_model(model, "model")
        
        # Log additional metrics
        mlflow.log_metric("training_completed", 1)
        mlflow.log_metric("timestamp", datetime.now().timestamp())
        
        logger.info("Training completed successfully!")


if __name__ == "__main__":
    main()
