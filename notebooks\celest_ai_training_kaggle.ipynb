# Install required packages
!pip install pytorch-lightning mlflow shap plotly

import pandas as pd
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import pytorch_lightning as pl
from pytorch_lightning.callbacks import ModelCheckpoint, EarlyStopping
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import classification_report, confusion_matrix, roc_auc_score, f1_score
import mlflow
import mlflow.pytorch
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, List, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

print(f"PyTorch version: {torch.__version__}")
print(f"CUDA available: {torch.cuda.is_available()}")
print(f"GPU device: {torch.cuda.get_device_name(0) if torch.cuda.is_available() else 'CPU only'}")

# REVISED CONFIGURATION FOR STABLE TRAINING
CONFIG = {
    'data_path': '/kaggle/input/hackthon/training_data_2010_2011.parquet',
    'sequence_length': 180,  # 3 hours of 1-minute data
    'patch_size': 12,       # 12-minute patches
    'd_model': 128,         # Model dimension
    'n_heads': 8,           # Number of attention heads
    'n_layers': 6,          # Number of transformer layers
    'dropout': 0.2,         # Increased from 0.1 to prevent overfitting
    'learning_rate': 1e-5,  # Drastically reduced from 1e-4 for stability
    'max_epochs': 30,       # Increased to allow for slower but stable learning
    'batch_size': 64,       # Increased batch size for more stable gradients
    'test_size': 0.2,
    'val_size': 0.1,
    'target_f1': 0.82,      # Target F1 score
    
    # Learning rate scheduler parameters
    'lr_scheduler_patience': 3,    # Wait 3 epochs before reducing LR
    'lr_scheduler_factor': 0.5,    # Reduce LR by half when plateau detected
    'lr_scheduler_verbose': True   # Print messages when LR is reduced
}

print("Training Configuration:")
for key, value in CONFIG.items():
    print(f"  {key}: {value}")

# CME Dataset Class
class CMEDataset(Dataset):
    def __init__(self, data: pd.DataFrame, sequence_length: int = 180, 
                 target_column: str = 'event_label', feature_columns: List[str] = None):
        self.data = data.sort_values('timestamp').reset_index(drop=True)
        self.sequence_length = sequence_length
        self.target_column = target_column
        
        if feature_columns is None:
            self.feature_columns = [
                'Bz_gsm', 'B_total', 'speed', 'density', 'temperature',
                'dynamic_pressure', 'clock_angle'
            ]
        else:
            self.feature_columns = feature_columns
        
        # Filter available columns
        self.feature_columns = [col for col in self.feature_columns if col in data.columns]
        
        # Prepare sequences
        self.sequences, self.targets = self._create_sequences()
        
        print(f"Created dataset with {len(self.sequences)} sequences")
        print(f"Features: {self.feature_columns}")
    
    def _create_sequences(self) -> Tuple[np.ndarray, np.ndarray]:
        sequences = []
        targets = []
        
        for i in range(len(self.data) - self.sequence_length):
            seq_data = self.data.iloc[i:i+self.sequence_length][self.feature_columns].values
            target = self.data.iloc[i+self.sequence_length][self.target_column]
            
            if not np.isnan(seq_data).any():
                sequences.append(seq_data)
                targets.append(target)
        
        return np.array(sequences), np.array(targets)
    
    def __len__(self):
        return len(self.sequences)
    
    def __getitem__(self, idx):
        return torch.FloatTensor(self.sequences[idx]), torch.LongTensor([self.targets[idx]])

# PatchTST Model Implementation
class PatchTSTModel(pl.LightningModule):
    def __init__(self, n_features: int, sequence_length: int, patch_size: int = 12,
                 d_model: int = 128, n_heads: int = 8, n_layers: int = 6,
                 dropout: float = 0.1, learning_rate: float = 1e-4,
                 class_weights: Optional[torch.Tensor] = None):
        super().__init__()
        
        self.save_hyperparameters()
        
        self.n_features = n_features
        self.sequence_length = sequence_length
        self.patch_size = patch_size
        self.n_patches = sequence_length // patch_size
        self.d_model = d_model
        self.learning_rate = learning_rate
        
        # Patch embedding
        self.patch_embedding = nn.Linear(patch_size * n_features, d_model)
        
        # Positional encoding
        self.pos_encoding = nn.Parameter(torch.randn(1, self.n_patches, d_model))
        
        # Transformer encoder
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=d_model, nhead=n_heads, dim_feedforward=d_model * 4,
            dropout=dropout, batch_first=True
        )
        self.transformer = nn.TransformerEncoder(encoder_layer, num_layers=n_layers)
        
        # Classification head
        self.classifier = nn.Sequential(
            nn.LayerNorm(d_model),
            nn.Dropout(dropout),
            nn.Linear(d_model, d_model // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(d_model // 2, 2)  # Binary classification
        )
        
        # Loss function
        if class_weights is not None:
            self.criterion = nn.CrossEntropyLoss(weight=class_weights)
        else:
            self.criterion = nn.CrossEntropyLoss()
        
        # Metrics storage
        self.validation_step_outputs = []
    
    def forward(self, x):
        batch_size = x.size(0)
        
        # Reshape to patches
        x = x.view(batch_size, self.n_patches, self.patch_size * self.n_features)
        
        # Patch embedding
        x = self.patch_embedding(x)
        
        # Add positional encoding
        x = x + self.pos_encoding
        
        # Transformer encoding
        x = self.transformer(x)
        
        # Global average pooling
        x = x.mean(dim=1)
        
        # Classification
        logits = self.classifier(x)
        
        return logits
    
    def training_step(self, batch, batch_idx):
        x, y = batch
        y = y.squeeze()
        
        logits = self(x)
        loss = self.criterion(logits, y)
        
        preds = torch.argmax(logits, dim=1)
        acc = (preds == y).float().mean()
        
        self.log('train_loss', loss, on_step=True, on_epoch=True, prog_bar=True)
        self.log('train_acc', acc, on_step=True, on_epoch=True, prog_bar=True)
        
        return loss
    
    def validation_step(self, batch, batch_idx):
        x, y = batch
        y = y.squeeze()
        
        logits = self(x)
        loss = self.criterion(logits, y)
        
        preds = torch.argmax(logits, dim=1)
        acc = (preds == y).float().mean()
        probs = torch.softmax(logits, dim=1)[:, 1]
        
        self.log('val_loss', loss, on_epoch=True, prog_bar=True)
        self.log('val_acc', acc, on_epoch=True, prog_bar=True)
        
        self.validation_step_outputs.append({
            'preds': preds, 'targets': y, 'probs': probs
        })
        
        return loss
    
    def on_validation_epoch_end(self):
        if not self.validation_step_outputs:
            return
        
        all_preds = torch.cat([x['preds'] for x in self.validation_step_outputs])
        all_targets = torch.cat([x['targets'] for x in self.validation_step_outputs])
        all_probs = torch.cat([x['probs'] for x in self.validation_step_outputs])
        
        # Calculate F1 score
        f1 = f1_score(all_targets.cpu().numpy(), all_preds.cpu().numpy())
        self.log('val_f1', f1, on_epoch=True, prog_bar=True)
        
        # Calculate AUC
        try:
            auc = roc_auc_score(all_targets.cpu().numpy(), all_probs.cpu().numpy())
            self.log('val_auc', auc, on_epoch=True, prog_bar=True)
        except ValueError:
            pass
        
        self.validation_step_outputs.clear()
    
    def configure_optimizers(self):
        """
        Configure optimizer and learning rate scheduler for stable training
        """
        # Define the optimizer with improved settings
        optimizer = optim.AdamW(
            self.parameters(),
            lr=self.learning_rate,
            weight_decay=1e-5
        )
        
        # Define the learning rate scheduler with improved parameters
        # It will watch 'val_loss' and reduce the LR if it plateaus
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            optimizer,
            mode='min',                    # We want to minimize the validation loss
            patience=3,                    # Wait 3 epochs with no improvement before reducing LR
            factor=0.5,                    # Reduce LR by half (e.g., 1e-5 -> 5e-6)
            verbose=True,                  # Print a message when the LR is reduced
            min_lr=1e-7                    # Don't reduce below this value
        )
        
        # Return the configuration for PyTorch Lightning
        return {
            'optimizer': optimizer,
            'lr_scheduler': {
                'scheduler': scheduler,
                'monitor': 'val_loss',     # The metric to monitor
                'interval': 'epoch',
                'frequency': 1,
            }
        }

# Data Loading and Preparation
def prepare_data(data_path: str, config: dict):
    print(f"Loading data from {data_path}")
    
    # Load data
    data = pd.read_parquet(data_path)
    print(f"Loaded {len(data)} records")
    
    # Display data info
    print("\nData Info:")
    print(f"Date range: {data['timestamp'].min()} to {data['timestamp'].max()}")
    print(f"Columns: {list(data.columns)}")
    
    # Check target distribution
    if 'event_label' in data.columns:
        target_dist = data['event_label'].value_counts()
        print(f"\nTarget distribution:")
        print(target_dist)
        print(f"Positive rate: {target_dist[1] / len(data):.3f}")
    
    # Select features
    feature_columns = [
        'Bz_gsm', 'B_total', 'speed', 'density', 'temperature',
        'dynamic_pressure', 'clock_angle'
    ]
    available_features = [col for col in feature_columns if col in data.columns]
    print(f"\nUsing features: {available_features}")
    
    # Add enhanced physics-informed features
    print("\n🔬 Adding enhanced physics-informed features...")
    data = add_enhanced_physics_features(data)
    
    # Update available features list
    physics_features = [
        'bz_southward_persistence', 'bz_intensity_30m', 'bz_to_btotal_ratio',
        'speed_acceleration_10m', 'speed_enhancement', 'density_enhancement',
        'dynamic_pressure', 'kinetic_to_magnetic_pressure', 'geoeffectiveness_proxy'
    ]
    available_features.extend([f for f in physics_features if f in data.columns])
    print(f"Enhanced features: {len(available_features)} total features")
    
    # Handle missing values
    data = data.dropna(subset=available_features + ['event_label'])
    print(f"After removing NaN: {len(data)} records")
    
    # Temporal split (important for time series)
    n_total = len(data)
    n_test = int(n_total * config['test_size'])
    n_val = int((n_total - n_test) * config['val_size'])
    
    train_data = data.iloc[:-n_test-n_val]
    val_data = data.iloc[-n_test-n_val:-n_test]
    test_data = data.iloc[-n_test:]
    
    print(f"\nData split: Train={len(train_data)}, Val={len(val_data)}, Test={len(test_data)}")
    
    # Scale features
    scaler = StandardScaler()
    train_data[available_features] = scaler.fit_transform(train_data[available_features])
    val_data[available_features] = scaler.transform(val_data[available_features])
    test_data[available_features] = scaler.transform(test_data[available_features])
    
    return train_data, val_data, test_data, scaler, available_features

# Load and prepare data
train_data, val_data, test_data, scaler, feature_columns = prepare_data(CONFIG['data_path'], CONFIG)

# Create datasets and data loaders
print("Creating datasets...")

train_dataset = CMEDataset(train_data, CONFIG['sequence_length'], feature_columns=feature_columns)
val_dataset = CMEDataset(val_data, CONFIG['sequence_length'], feature_columns=feature_columns)
test_dataset = CMEDataset(test_data, CONFIG['sequence_length'], feature_columns=feature_columns)

# Create data loaders
train_loader = DataLoader(train_dataset, batch_size=CONFIG['batch_size'], shuffle=True)
val_loader = DataLoader(val_dataset, batch_size=CONFIG['batch_size'], shuffle=False)
test_loader = DataLoader(test_dataset, batch_size=CONFIG['batch_size'], shuffle=False)

print(f"Train batches: {len(train_loader)}")
print(f"Val batches: {len(val_loader)}")
print(f"Test batches: {len(test_loader)}")

# Calculate class weights for imbalanced data
train_targets = []
for batch in train_loader:
    _, targets = batch
    train_targets.extend(targets.squeeze().tolist())

class_counts = np.bincount(train_targets)
class_weights = torch.FloatTensor(len(class_counts) / class_counts)

print(f"\nClass distribution: {class_counts}")
print(f"Class weights: {class_weights}")

# Initialize MLflow experiment
mlflow.set_experiment("celest-ai-kaggle-training")

with mlflow.start_run(run_name="patchtst-synthetic-data"):
    # Log configuration
    mlflow.log_params(CONFIG)
    mlflow.log_param("n_features", len(feature_columns))
    mlflow.log_param("train_samples", len(train_dataset))
    mlflow.log_param("val_samples", len(val_dataset))
    mlflow.log_param("test_samples", len(test_dataset))
    
    # Initialize model
    model = PatchTSTModel(
        n_features=len(feature_columns),
        sequence_length=CONFIG['sequence_length'],
        patch_size=CONFIG['patch_size'],
        d_model=CONFIG['d_model'],
        n_heads=CONFIG['n_heads'],
        n_layers=CONFIG['n_layers'],
        dropout=CONFIG['dropout'],
        learning_rate=CONFIG['learning_rate'],
        class_weights=class_weights
    )
    
    print(f"\nModel initialized with {sum(p.numel() for p in model.parameters())} parameters")
    
    # Setup callbacks
    checkpoint_callback = ModelCheckpoint(
        monitor='val_f1',
        dirpath='./checkpoints',
        filename='patchtst-{epoch:02d}-{val_f1:.3f}',
        save_top_k=3,
        mode='max'  # Maximize F1 score
    )
    
    early_stopping = EarlyStopping(
        monitor='val_f1',
        patience=8,
        mode='max'
    )
    
    # Setup trainer with improved stability settings
    trainer = pl.Trainer(
        max_epochs=CONFIG['max_epochs'],
        callbacks=[checkpoint_callback, early_stopping],
        accelerator='gpu' if torch.cuda.is_available() else 'cpu',
        devices=1,
        log_every_n_steps=50,
        precision=16 if torch.cuda.is_available() else 32,  # Mixed precision for GPU
        gradient_clip_val=1.0,          # Prevent gradient explosion
        deterministic=True              # For reproducible results
    )
    
    # Add progress monitoring
    print("\n🚀 Starting improved training with:")
    print(f"   📉 Learning Rate: {CONFIG['learning_rate']}")
    print(f"   🎯 Target F1 Score: {CONFIG['target_f1']}")
    print(f"   📊 Batch Size: {CONFIG['batch_size']}")
    print(f"   🔄 Max Epochs: {CONFIG['max_epochs']}")
    print(f"   🛡️ Dropout: {CONFIG['dropout']}")
    print("\n⏳ Training in progress... Watch for:")
    print("   ✅ F1 scores should start improving from Epoch 2-3")
    print("   📈 Look for 'ReduceLROnPlateau reducing learning rate' messages")
    print("   🎯 Target: F1 Score ≥ 0.82")
    print("\n" + "="*60)
    
    # Train model
    trainer.fit(model, train_loader, val_loader)
    
    print("\n" + "="*60)
    print("✅ Training completed!")

# Model Evaluation and Results
print("\n📊 Evaluating model on test set...")

# Test the model
trainer.test(model, test_loader)

# Get predictions for detailed analysis
model.eval()
all_preds = []
all_targets = []
all_probs = []

with torch.no_grad():
    for batch in test_loader:
        x, y = batch
        if torch.cuda.is_available():
            x, y = x.cuda(), y.cuda()
        
        logits = model(x)
        preds = torch.argmax(logits, dim=1)
        probs = torch.softmax(logits, dim=1)[:, 1]
        
        all_preds.extend(preds.cpu().numpy())
        all_targets.extend(y.squeeze().cpu().numpy())
        all_probs.extend(probs.cpu().numpy())

# Calculate final metrics
final_f1 = f1_score(all_targets, all_preds)
final_auc = roc_auc_score(all_targets, all_probs)

print(f"\n🎯 FINAL RESULTS:")
print(f"F1 Score: {final_f1:.4f} (Target: {CONFIG['target_f1']})")
print(f"AUC Score: {final_auc:.4f}")
print(f"Target Achieved: {'✅ YES' if final_f1 >= CONFIG['target_f1'] else '❌ NO'}")

# Log final metrics to MLflow
mlflow.log_metric("final_f1_score", final_f1)
mlflow.log_metric("final_auc_score", final_auc)
mlflow.log_metric("target_achieved", 1 if final_f1 >= CONFIG['target_f1'] else 0)

# Classification report
print("\n📋 Classification Report:")
print(classification_report(all_targets, all_preds, target_names=['Normal', 'CME Event']))

# Confusion Matrix
cm = confusion_matrix(all_targets, all_preds)
plt.figure(figsize=(8, 6))
sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', 
            xticklabels=['Normal', 'CME Event'], 
            yticklabels=['Normal', 'CME Event'])
plt.title('Confusion Matrix - CELEST AI PatchTST Model')
plt.ylabel('True Label')
plt.xlabel('Predicted Label')
plt.show()

# Save model artifacts
mlflow.pytorch.log_model(model, "patchtst_model")
print("\n💾 Model saved to MLflow")

print("\n🌟 Training and evaluation completed successfully!")
print(f"\n📈 Summary:")
print(f"   • Model: PatchTST Transformer")
print(f"   • Data: Synthetic CME detection dataset")
print(f"   • F1 Score: {final_f1:.4f}")
print(f"   • AUC Score: {final_auc:.4f}")
print(f"   • Target F1 > 0.82: {'✅ Achieved' if final_f1 >= 0.82 else '❌ Not achieved'}")
print(f"\n🚀 Ready for real data integration!")