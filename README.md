# CELEST AI: Prototype for Geo-Effective CME Detection

This repository contains the prototype implementation for the CELEST AI system, a high-precision, in-situ warning system for geo-effective Coronal Mass Ejections (CMEs) using L1 satellite data.

**Problem Statement:** Provide a deterministic, high-confidence 'impact imminent' alert (30-60 minutes) to safeguard India's national space assets from severe space weather.

**Prototype Goal:** To build and validate the core data-to-alert pipeline using historical L1 data (NASA's OMNIWeb) as a proxy for Aditya-L1. The system will ingest time-series solar wind data, apply a physics-driven labeling engine, train a Transformer-based model (PatchTST), and expose the alerting capability via a REST API and a demonstration dashboard.

---

## Architecture Overview (Prototype)

```
OMNIWeb Data → PDCE Labeling → PatchTST Training → FastAPI Service → Streamlit Dashboard
     ↓              ↓              ↓                ↓                    ↓
Historical L1   Physics-Based   Transformer     REST API          Operations UI
Solar Wind      Event Labels    Time-Series     /api/v1/status    + SHAP XAI
Parameters                      Forecasting
```

1. **Data Ingestion:** Historical 1-minute solar wind data is fetched from NASA's OMNIWeb service.
2. **PDCE Labeling:** A **P**hysics-**D**riven **C**onsensus **E**ngine (`pdce.py`) applies heuristics based on solar physics principles (e.g., strong southward Bz, high solar wind speed) to generate ground-truth labels for training.
3. **Model Training:** A PatchTST (Transformer) model is trained on the labeled time-series data to learn the precursors to geo-effective events. Experiments are tracked with **MLflow**.
4. **Inference API:** A **FastAPI** service (`api.py`) loads the trained model and exposes a `GET /api/v1/status` endpoint for real-time inference.
5. **Dashboard & XAI:** A **Streamlit** dashboard (`dashboard.py`) provides a user interface to simulate data, view alerts, visualize the input data, and understand the model's decision-making via **SHAP** plots.

---

## Tech Stack

- **Data Handling:** Pandas, NumPy, SunPy
- **Machine Learning:** PyTorch, scikit-learn
- **MLOps:** MLflow
- **Explainable AI (XAI):** SHAP
- **API:** FastAPI, Uvicorn
- **Dashboard:** Streamlit, Plotly
- **Containerization:** Docker

---

## Project Structure

```
celest-ai-prototype/
├── data/
│   ├── raw/          # Raw downloaded data (OMNIWeb, ICME catalog)
│   └── processed/    # Cleaned, labeled data for training (e.g., training.parquet)
├── notebooks/
│   ├── 01-data-exploration.ipynb
│   └── 02-model-prototyping.ipynb
├── scripts/
│   └── fetch_data.py   # Script to download OMNIWeb and catalog data
├── src/
│   ├── __init__.py
│   ├── api.py          # FastAPI application
│   ├── dashboard.py    # Streamlit dashboard application
│   ├── model_inference.py # Logic for loading model and making predictions
│   ├── model_train.py  # Script to train the PatchTST model
│   ├── pdce.py         # Physics-Driven Consensus Engine (labeling)
│   └── preprocessing.py # Data cleaning and feature engineering
├── tests/
│   ├── test_preprocessing.py
│   └── test_pdce.py
├── .gitignore
├── Dockerfile          # Dockerfile for the FastAPI service
├── environment.yml     # Conda environment file
├── mlruns/             # MLflow experiment tracking output (add to .gitignore)
└── README.md
```

---

## Setup & Installation

1. **Clone the repository:**
   ```bash
   git clone <repository-url>
   cd celest-ai-prototype
   ```

2. **Create and activate the Conda environment:**
   ```bash
   conda env create -f environment.yml
   conda activate celestai
   ```

---

## How to Run the System

**Step 1: Fetch and Process Data**

Run the data pipeline to download OMNIWeb data and generate labels.

```bash
# Downloads raw data into data/raw/
python scripts/fetch_data.py

# Cleans data and generates labels, saving output to data/processed/
python src/preprocessing.py
```

**Step 2: Train the Model**

Run the training script. This will train the PatchTST model and log the experiment and artifacts to MLflow.

```bash
python src/model_train.py
```

To view the results, run the MLflow UI in a separate terminal:
```bash
mlflow ui
```
Navigate to `http://127.0.0.1:5000` in your browser.

**Step 3: Run the Inference API**

Start the FastAPI server. It will automatically load the latest production model from MLflow.

```bash
uvicorn src.api:app --reload
```
The API will be available at `http://127.0.0.1:8000`. You can test it at `http://127.0.0.1:8000/docs`.

**Step 4: Launch the Operations Dashboard**

In a new terminal, run the Streamlit dashboard.

```bash
streamlit run src/dashboard.py
```
Navigate to the local URL provided by Streamlit (usually `http://localhost:8501`) to interact with the system.

---

## 20-Day Development Plan

### Phase 1: Foundation, Data & Physics-Driven Labeling (Days 1-7)
- [x] Setup project structure and environment
- [ ] Data acquisition pipeline (OMNIWeb + ICME catalog)
- [ ] Preprocessing and PDCE v1 (heuristic labeling)
- [ ] Baseline model and MLflow setup

### Phase 2: Advanced Modeling & API (Days 8-14)
- [ ] PatchTST model implementation and training
- [ ] PDCE v2 (probabilistic model)
- [ ] FastAPI inference service and containerization

### Phase 3: Visualization, XAI & Integration (Days 15-20)
- [ ] Streamlit dashboard with XAI integration
- [ ] End-to-end testing and documentation
- [ ] Final polish and presentation preparation

---

## Collaboration Guidelines

- **Branching:** Use the `git-flow` model. Create feature branches from `develop` (e.g., `feature/pdce-labeling`). Do not commit directly to `main`.
- **Commits:** Write clear, concise commit messages (e.g., "feat: Implement heuristic PDCE labeling").
- **Code Style:** Follow PEP 8 guidelines. Use a linter like `flake8` or `black` to maintain consistency.
- **Pull Requests:** All new features or fixes must be submitted as Pull Requests to `develop`. Require at least one peer review before merging.

---

## Key Performance Targets

- **Latency:** < 5 seconds for inference
- **Uptime:** 99.9% availability
- **Accuracy:** F1 score > 0.82 (baseline from ACE/WIND validation)
- **Lead Time:** 30-60 minute advance warning

---

## Contact & Support

For questions or issues, please contact the CELEST AI development team.
